<script setup>
// The entire script block is IDENTICAL to the one you provided. It is correct.
import { computed, h, ref } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import { FlexRender, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useVueTable } from '@tanstack/vue-table'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/Components/ui/table';
import { Button } from '@/Components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/Components/ui/dropdown-menu';
import { MoreHorizontal, ArrowUpDown } from 'lucide-vue-next';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/Components/ui/card';
import { Input } from '@/Components/ui/input';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';

// We REMOVE defineOptions({ layout: AdminLayout });
// to follow our stable manual wrapper pattern.

const props = defineProps({
    work_zones: Array,
});

const data = computed(() => props.work_zones);
const columns = [ { accessorKey: 'name', header: 'Name' }, { accessorKey: 'latitude', header: 'Latitude' }, { accessorKey: 'longitude', header: 'Longitude' }, { accessorKey: 'radius_meters', header: 'Radius' }, { id: 'actions', header: () => h('div', { class: 'text-right' }, 'Actions'), enableHiding: false }, ];
const table = useVueTable({ data: data.value, columns, getCoreRowModel: getCoreRowModel(), getFilteredRowModel: getFilteredRowModel(), getPaginationRowModel: getPaginationRowModel(), getSortedRowModel: getSortedRowModel(), });
const confirmingZoneDeletion = ref(false);
const zoneToDelete = ref(null);
const confirmZoneDeletion = (zone) => { zoneToDelete.value = zone; confirmingZoneDeletion.value = true; };
const deleteZone = () => { router.delete(route('admin.work-zones.destroy', zoneToDelete.value.id), { preserveState: false, onSuccess: () => closeModal() }); };
const closeModal = () => { confirmingZoneDeletion.value = false; zoneToDelete.value = null; };
const expandedRows = ref({});
const toggleRow = (id) => { const isOpen = expandedRows.value[id]; expandedRows.value = {}; if (!isOpen) expandedRows.value[id] = true; };
const editZone = (id) => router.visit(route('admin.work-zones.edit', id));
</script>

<template>
    <Head title="Manage Work Zones" />

    <!-- We now wrap the entire page in the AdminLayout component -->
    <AdminLayout>
        <!-- Provide the content for the header slot -->
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                Manage Work Zones
            </h2>
        </template>
        <!-- Provide the content for the actions slot -->
        <template #actions>
            <Link :href="route('admin.work-zones.create')">
                <Button>Add New Zone</Button>
            </Link>
        </template>

        <!-- The rest of the page content goes into the default slot -->
        <div class="space-y-4">
            <div class="flex items-center">
                <Input
                    class="max-w-sm"
                    placeholder="Filter by name..."
                    :model-value="table.getColumn('name')?.getFilterValue()"
                    @update:model-value="table.getColumn('name')?.setFilterValue($event)"
                />
            </div>

            <!-- Desktop Table View -->
            <div class="hidden rounded-md border md:block">
                <Table>
                    <TableHeader>
                        <TableRow v-for="headerGroup in table.getHeaderGroups()" :key="headerGroup.id">
                            <TableHead v-for="header in headerGroup.headers" :key="header.id">
                                <button v-if="header.column.getCanSort()" @click="header.column.toggleSorting(header.column.getIsSorted() === 'asc')" class="flex items-center">
                                    <FlexRender :render="header.column.columnDef.header" :props="header.getContext()" />
                                    <ArrowUpDown class="ml-2 h-4 w-4" />
                                </button>
                                 <FlexRender v-else :render="header.column.columnDef.header" :props="header.getContext()" />
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-if="!table.getRowModel().rows?.length"><TableCell :colspan="columns.length" class="h-24 text-center">No work zones found.</TableCell></TableRow>
                        <TableRow v-for="row in table.getRowModel().rows" :key="row.id">
                            <TableCell v-for="cell in row.getVisibleCells()" :key="cell.id">
                                <template v-if="cell.column.id === 'radius_meters'">{{ cell.getValue() }}m</template>
                                <template v-else-if="cell.column.id === 'actions'">
                                    <div class="text-right">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger as-child><Button variant="ghost" class="h-8 w-8 p-0"><MoreHorizontal class="h-4 w-4" /></Button></DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem @click="editZone(row.original.id)">Edit</DropdownMenuItem>
                                                <DropdownMenuItem @click="confirmZoneDeletion(row.original)" class="text-red-600">Delete</DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                </template>
                                <template v-else>{{ cell.getValue() }}</template>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>

            <!-- Mobile Card View -->
            <div class="grid gap-4 md:hidden">
                <div v-if="!table.getRowModel().rows?.length" class="text-center text-muted-foreground col-span-full">No results found.</div>
                <Card v-for="row in table.getRowModel().rows" :key="`mobile-${row.id}`" @click="toggleRow(row.original.id)">
                    <CardHeader>
                        <CardTitle class="text-lg">{{ row.original.name }}</CardTitle>
                        <CardDescription>Radius: {{ row.original.radius_meters }}m</CardDescription>
                    </CardHeader>
                    <CardContent v-if="expandedRows[row.original.id]" @click.stop class="space-y-4 pt-4 border-t">
                        <div class="flex justify-between text-sm"><span class="text-muted-foreground">Latitude:</span><span class="font-mono">{{ row.original.latitude }}</span></div>
                        <div class="flex justify-between text-sm"><span class="text-muted-foreground">Longitude:</span><span class="font-mono">{{ row.original.longitude }}</span></div>
                        <div class="flex justify-end gap-2 pt-2">
                            <Button variant="secondary" @click="editZone(row.original.id)">Edit</Button>
                            <Button variant="destructive" class="bg-red-600 text-white hover:bg-red-700" @click="confirmZoneDeletion(row.original)">Delete</Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
            
            <!-- Pagination would go here if needed -->
        </div>

        <Modal :show="confirmingZoneDeletion" @close="closeModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">Are you sure?</h2>
                <p v-if="zoneToDelete" class="mt-1 text-sm text-gray-600">You are about to permanently delete the **{{ zoneToDelete.name }}** work zone.</p>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal">Cancel</SecondaryButton>
                    <DangerButton class="ms-3" @click="deleteZone">Delete Zone</DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>
<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WorkZone>
 */
class WorkZoneFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->company . ' Office',
            'latitude' => fake()->latitude(33, 49), // Example: USA
            'longitude' => fake()->longitude(-125, -67),
            'radius_meters' => fake()->numberBetween(100, 500),
        ];
    }
}

<?php

namespace App\Rules;

use App\Models\WorkZone;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class NoZoneOverlap implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $latitude = request()->input('latitude');
        $longitude = request()->input('longitude');
        $radius = request()->input('radius_meters');
        $editingZoneId = request()->input('editing_zone_id'); // Get the ID of the zone we're editing

        if (!$latitude || !$longitude || !$radius) {
            return;
        }

        // Start with all zones, then optionally filter out the one we are editing
        $query = WorkZone::query();
        if ($editingZoneId) {
            $query->where('id', '!=', $editingZoneId);
        }
        $existingZones = $query->get();

        foreach ($existingZones as $zone) {
            $distance = $this->haversineDistance($latitude, $longitude, $zone->latitude, $zone->longitude);
            if ($distance < ($radius + $zone->radius_meters)) {
                $fail("This work zone overlaps with an existing zone: '{$zone->name}'.");
                return;
            }
        }
    }

    private function haversineDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371000;
        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);
        $a = sin($dLat / 2) * sin($dLat / 2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon / 2) * sin($dLon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        return $earthRadius * $c;
    }
}
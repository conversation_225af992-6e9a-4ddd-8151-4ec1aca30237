<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Attendance extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'employee_profile_id',
        'shift_id',
        'check_in_time',
        'check_in_latitude',
        'check_in_longitude',
        'check_in_selfie_path',
        'check_in_notes',
        'check_out_time',
        'check_out_latitude',
        'check_out_longitude',
        'check_out_selfie_path',
        'check_out_notes',
        'status',
        'overtime_minutes',
        'undertime_minutes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'check_in_time' => 'datetime',
        'check_out_time' => 'datetime',
    ];

    /**
     * Get the employee profile that owns the attendance record.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(EmployeeProfile::class, 'employee_profile_id');
    }

    /**
     * Get the shift associated with the attendance record.
     */
    public function shift(): BelongsTo
    {
        return $this->belongsTo(Shift::class);
    }
}
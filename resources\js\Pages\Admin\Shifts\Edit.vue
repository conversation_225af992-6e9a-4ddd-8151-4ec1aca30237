<script setup>
// The script is nearly the same, we just REMOVE defineOptions.
import { Head, useForm } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Button } from '@/Components/ui/button';
import InputError from '@/Components/InputError.vue';
import { Checkbox } from '@/Components/ui/checkbox';

// We REMOVE defineOptions({ layout: AdminLayout });
// This is the key change to adopt the manual wrapper pattern.

const props = defineProps({
    shift: Object,
});

const form = useForm({
    name: props.shift.name,
    start_time: props.shift.start_time.substring(0, 5),
    end_time: props.shift.end_time.substring(0, 5),
    unpaid_break_minutes: props.shift.unpaid_break_minutes,
    spans_two_days: props.shift.spans_two_days,
    check_in_allowance_before_minutes: props.shift.policy.check_in_allowance_before_minutes,
    late_grace_period_minutes: props.shift.policy.late_grace_period_minutes,
    early_leave_grace_period_minutes: props.shift.policy.early_leave_grace_period_minutes,
    overtime_grace_period_minutes: props.shift.policy.overtime_grace_period_minutes,
});

const submit = () => {
    form.put(route('admin.shifts.update', props.shift.id));
};
</script>

<template>
    <Head title="Edit Shift" />

    <!-- We now wrap the entire page in the AdminLayout component -->
    <AdminLayout>
        <!-- Provide the content for the header slot -->
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                Edit Shift: {{ shift.name }}
            </h2>
        </template>
        
        <!-- This page has no actions in the header, so the #actions slot is omitted. -->

        <!-- The rest of the page content goes into the default slot -->
        <div class="space-y-4">
            <form @submit.prevent="submit">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader><CardTitle>Shift Details</CardTitle><CardDescription>Define the name, hours, and breaks for this shift.</CardDescription></CardHeader>
                        <CardContent class="space-y-4">
                            <div><Label for="name">Shift Name</Label><Input id="name" v-model="form.name" type="text" required class="mt-1" /><InputError class="mt-2" :message="form.errors.name" /></div>
                            <div class="flex gap-4">
                                <div class="flex-1"><Label for="start_time">Start Time</Label><Input id="start_time" v-model="form.start_time" type="time" required class="mt-1" /><InputError class="mt-2" :message="form.errors.start_time" /></div>
                                <div class="flex-1"><Label for="end_time">End Time</Label><Input id="end_time" v-model="form.end_time" type="time" required class="mt-1" /><InputError class="mt-2" :message="form.errors.end_time" /></div>
                            </div>
                            <div class="flex items-center space-x-2 pt-2">
                                <Checkbox id="spans_two_days" v-model="form.spans_two_days" :checked="form.spans_two_days" />
                                <label for="spans_two_days" class="text-sm font-medium">Shift spans across midnight (ends next day)</label>
                                <InputError class="mt-2" :message="form.errors.spans_two_days" />
                            </div>
                            <div><Label for="unpaid_break_minutes">Unpaid Break (in minutes)</Label><Input id="unpaid_break_minutes" v-model="form.unpaid_break_minutes" type="number" required class="mt-1" /><InputError class="mt-2" :message="form.errors.unpaid_break_minutes" /></div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader><CardTitle>Shift Policies</CardTitle><CardDescription>Set the rules and grace periods in minutes.</CardDescription></CardHeader>
                        <CardContent class="space-y-4">
                            <div><Label for="check_in_allowance_before_minutes">Check-in Allowance (before shift starts)</Label><Input id="check_in_allowance_before_minutes" v-model="form.check_in_allowance_before_minutes" type="number" required class="mt-1" /><InputError class="mt-2" :message="form.errors.check_in_allowance_before_minutes" /></div>
                            <div><Label for="late_grace_period_minutes">Late Grace Period (after shift starts)</Label><Input id="late_grace_period_minutes" v-model="form.late_grace_period_minutes" type="number" required class="mt-1" /><InputError class="mt-2" :message="form.errors.late_grace_period_minutes" /></div>
                             <div><Label for="early_leave_grace_period_minutes">Early Leave Grace Period (before shift ends)</Label><Input id="early_leave_grace_period_minutes" v-model="form.early_leave_grace_period_minutes" type="number" required class="mt-1" /><InputError class="mt-2" :message="form.errors.early_leave_grace_period_minutes" /></div>
                             <div><Label for="overtime_grace_period_minutes">Overtime Grace Period (after shift ends)</Label><Input id="overtime_grace_period_minutes" v-model="form.overtime_grace_period_minutes" type="number" required class="mt-1" /><InputError class="mt-2" :message="form.errors.overtime_grace_period_minutes" /></div>
                        </CardContent>
                    </Card>
                </div>

                <div class="flex items-center justify-end mt-6">
                    <Button :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                        Update Shift
                    </Button>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For now, let's skip the database constraint and handle duplicates in the application logic
        // The constraint would require a computed column which is complex in Laravel migrations
        // We've already fixed the seeder to prevent duplicates
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('attendances', function (Blueprint $table) {
            $table->dropUnique('unique_employee_daily_attendance');
        });
    }
};

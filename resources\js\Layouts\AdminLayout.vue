<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'; // Make sure watch is imported
import { Link, usePage, router } from '@inertiajs/vue3';
import { Home, Users, Folder, File, PanelLeft, Map } from 'lucide-vue-next';
import { But<PERSON> } from '@/Components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/Components/ui/sheet';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/Components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/Components/ui/avatar';
// We are REMOVING vue-sonner imports as they are the cause of the problem.

// --- STATE FOR OUR CUSTOM, WORKING NOTIFICATION ---
const showNotification = ref(false);
const notificationMessage = ref('');
const notificationIsError = ref(false);

const isMobileMenuOpen = ref(false);
const page = usePage();
const user = page.props.auth.user;
const initials = user ? user.name.substring(0, 2).toUpperCase() : '';

// --- THIS IS THE CORRECT WATCHER THAT YOU PROVIDED ---
watch(() => page.props.flash, (flash) => {
    if (flash?.success) {
        notificationMessage.value = flash.success;
        showNotification.value = true;
        // Set a timer to hide the notification after 4 seconds
        setTimeout(() => {
            showNotification.value = false;
        }, 4000);
    }
    // We can add error handling here later if needed
    if (flash?.error) {
        notificationMessage.value = flash.error;
        showNotification.value = true;
        setTimeout(() => {
            showNotification.value = false;
        }, 4000);
    }
}, { deep: true });


const navItems = [
    { title: 'Dashboard', href: route('admin.dashboard'), icon: Home, startsWith: '/admin/dashboard' },
    { title: 'Employees', href: route('admin.employees.index'), icon: Users, startsWith: '/admin/employees' },
    { title: 'Shifts', href: route('admin.shifts.index'), icon: Folder, startsWith: '/admin/shifts' },
    { title: 'Assignments', href: route('admin.shift-assignments.index'), icon: Users, startsWith: '/admin/shift-assignments' },
    { title: 'Work Zones', href: route('admin.work-zones.index'), icon: Map, startsWith: '/admin/work-zones' },
];

let removeFinishListener;

onMounted(() => {
    removeFinishListener = router.on('finish', (event) => {
        const flash = page.props.flash;
        if (flash?.success) {
            notificationMessage.value = flash.success;
            notificationIsError.value = false;
            showNotification.value = true;
            setTimeout(() => { showNotification.value = false; }, 4000);
        }
        if (flash?.error) {
            notificationMessage.value = flash.error;
            notificationIsError.value = true;
            showNotification.value = true;
            setTimeout(() => { showNotification.value = false; }, 4000);
        }
    });
});

// Clean up the listener when the layout is unmounted
onUnmounted(() => {
    removeFinishListener();
});
</script>

<template>
    <div>
        <div v-if="showNotification" 
             :class="[
                'fixed top-5 left-1/2 -translate-x-1/2 z-[9999] text-white p-4 rounded-md shadow-lg',
                notificationIsError ? 'bg-red-600' : 'bg-green-600'
             ]"
        >
            {{ notificationMessage }}
        </div>

        <div class="min-h-screen w-full">
            <aside class="hidden md:flex flex-col fixed inset-y-0 left-0 z-10 w-56 border-r bg-background">
                <div class="flex h-14 items-center border-b px-6"><Link href="/" class="flex items-center gap-2 font-semibold"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" class="h-6 w-6"><path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" /><path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9" /><path d="M12 3v6" /></svg><span>Time Tracker</span></Link></div>
                <nav class="flex-1 overflow-auto py-4 px-4 text-sm font-medium"><Link v-for="item in navItems" :key="item.title" :href="item.href" class="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary" :class="{ 'bg-muted text-primary': page.url.startsWith(item.startsWith) }"><component :is="item.icon" class="h-4 w-4" />{{ item.title }}</Link></nav>
            </aside>

            <div class="flex flex-col md:ml-56">
                <header class="flex h-14 items-center gap-4 border-b bg-background px-4 lg:px-6 sticky top-0 z-30">
                    <Sheet v-model:open="isMobileMenuOpen">
                        <SheetTrigger as-child><Button variant="outline" size="icon" class="shrink-0 md:hidden"><PanelLeft class="h-5 w-5" /></Button></SheetTrigger>
                        <SheetContent side="left" class="flex flex-col bg-background p-6">
                           <nav class="grid gap-2 text-base font-medium">
                                <Link v-for="item in navItems" :key="`mobile-${item.title}`" :href="item.href" @click="isMobileMenuOpen = false" class="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"><component :is="item.icon" class="h-5 w-5" />{{ item.title }}</Link>
                           </nav>
                        </SheetContent>
                    </Sheet>
                    <div class="flex-1">
                        <slot name="header" />
                    </div>

                    <div class="flex items-center gap-4">
                        <slot name="actions" />

                        <DropdownMenu>
                            <DropdownMenuTrigger as-child><Button variant="secondary" size="icon" class="rounded-full"><Avatar class="h-8 w-8"><AvatarFallback>{{ initials }}</AvatarFallback></Avatar></Button></DropdownMenuTrigger>
                            <DropdownMenuContent align="end"><DropdownMenuLabel>My Account</DropdownMenuLabel><DropdownMenuSeparator /><DropdownMenuItem><Link :href="route('logout')" method="post" as="button" class="w-full text-left">Log Out</Link></DropdownMenuItem></DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </header>
                <main class="flex-1 p-4 lg:p-6">
                    <slot />
                </main>
            </div>
        </div>
    </div>
</template>
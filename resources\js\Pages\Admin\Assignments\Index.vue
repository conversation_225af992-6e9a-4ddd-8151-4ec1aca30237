<script setup>
// The script block is IDENTICAL, except for one line inside useVueTable.
import { ref, watch, computed, h } from 'vue';
import { Head, useForm, router } from '@inertiajs/vue3';
import axios from 'axios';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import { FlexRender, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useVueTable } from '@tanstack/vue-table'
import { Check, ChevronsUpDown, Search, ArrowUpDown, MoreHorizontal, Trash2 } from 'lucide-vue-next';
import { cn } from '@/lib/utils';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/Components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Label } from '@/Components/ui/label';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import InputError from '@/Components/InputError.vue';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/Components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Badge } from '@/Components/ui/badge';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';

const props = defineProps({ employees: Array, shifts: Array, work_zones: Array, assignments: Array, });
const form = useForm({ employee_ids: [], shift_id: null, work_zone_id: null, start_date: new Date().toISOString().split('T')[0], end_date: new Date(new Date().setDate(new Date().getDate() + 6)).toISOString().split('T')[0], });
const availableEmployees = ref([]);
const isLoadingEmployees = ref(false);
async function fetchAvailableEmployees() { if (!form.start_date || !form.end_date) { availableEmployees.value = []; return; } isLoadingEmployees.value = true; form.employee_ids = []; try { const response = await axios.get(route('admin.shift-assignments.available-employees'), { params: { start_date: form.start_date, end_date: form.end_date } }); availableEmployees.value = response.data; } catch (error) { console.error("Failed to fetch available employees:", error); availableEmployees.value = []; } finally { isLoadingEmployees.value = false; } }
watch(() => [form.start_date, form.end_date], fetchAvailableEmployees, { immediate: true });
const submit = () => { form.post(route('admin.shift-assignments.store'), { preserveScroll: true, onSuccess: () => { form.reset('employee_ids'); fetchAvailableEmployees(); }, }); };
const isPopoverOpen = ref(false);
const selectedEmployees = computed(() => form.employee_ids.map(id => availableEmployees.value.find(employee => employee.id === id)).filter(Boolean));
const assignmentData = computed(() => props.assignments);
const assignmentColumns = [
    {
        // We can't use an accessorKey for a nested object. We use an accessorFn instead.
        accessorFn: row => `${row.employee?.first_name || ''} ${row.employee?.last_name || ''}`,
        id: 'employeeName', // A unique ID for this column
        header: ({ column }) => h(Button, { variant: 'ghost', onClick: () => column.toggleSorting(column.getIsSorted() === 'asc') }, () => ['Employee', h(ArrowUpDown, { class: 'ml-2 h-4 w-4' })]),
    },
    { accessorKey: 'shift.name', header: 'Shift' },
    { accessorKey: 'work_zone.name', header: 'Work Zone' },
    { accessorKey: 'start_date', header: 'Start Date', cell: ({ row }) => new Date(row.getValue('start_date')).toLocaleDateString() },
    { accessorKey: 'end_date', header: 'End Date', cell: ({ row }) => row.getValue('end_date') ? new Date(row.getValue('end_date')).toLocaleDateString() : 'Ongoing' },
    { id: 'actions', enableHiding: false, cell: ({ row }) => h('div', { class: 'text-right' }, h(Button, { variant: 'ghost', size: 'icon', class: 'h-8 w-8', onClick: (e) => { e.stopPropagation(); confirmAssignmentDeletion(row.original); } }, () => h(Trash2, { class: 'h-4 w-4' }))) }
];
const assignmentSorting = ref([]);
const assignmentColumnFilters = ref([]);

// ### THE FIX IS INSIDE THIS OBJECT ###
const assignmentTable = useVueTable({
    data: assignmentData.value,
    columns: assignmentColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
        get sorting() { return assignmentSorting.value },
        get columnFilters() { return assignmentColumnFilters.value },
    },
    onSortingChange: updater => assignmentSorting.value = typeof updater === 'function' ? updater(assignmentSorting.value) : updater,
    // This was the missing line that connects the input to the table's state
    onColumnFiltersChange: updater => assignmentColumnFilters.value = typeof updater === 'function' ? updater(assignmentColumnFilters.value) : updater,

});

const confirmingAssignmentDeletion = ref(false);
const assignmentToDelete = ref(null);
const confirmAssignmentDeletion = (assignment) => { assignmentToDelete.value = assignment; confirmingAssignmentDeletion.value = true; };
const deleteAssignment = () => { router.delete(route('admin.shift-assignments.destroy', assignmentToDelete.value.id), { preserveState: false, onSuccess: () => closeAssignmentModal() }); };
const closeAssignmentModal = () => { confirmingAssignmentDeletion.value = false; assignmentToDelete.value = null; };
const expandedRows = ref({});
const toggleRow = (id) => { const isOpen = expandedRows.value[id]; expandedRows.value = {}; if (!isOpen) expandedRows.value[id] = true; };
</script>

<template>

    <Head title="Shift Assignments" />

    <AdminLayout>
        <!-- The entire template is IDENTICAL to your working version -->
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                Shift Assignments
            </h2>
        </template>

        <div class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Left Column: Assignment Form -->
                <div class="lg:col-span-1">
                    <form @submit.prevent="submit">
                        <Card>
                            <CardHeader>
                                <CardTitle>Assign New Shifts</CardTitle>
                            </CardHeader>
                            <CardContent class="space-y-6">
                                <fieldset class="space-y-2">
                                    <legend class="px-1 text-sm font-medium">1. Select Date Range</legend>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div><Label for="start_date">From</Label><Input id="start_date"
                                                v-model="form.start_date" type="date" required class="mt-1" /></div>
                                        <div><Label for="end_date">To</Label><Input id="end_date"
                                                v-model="form.end_date" type="date" required class="mt-1" /></div>
                                    </div>
                                    <InputError class="mt-2"
                                        :message="form.errors.start_date || form.errors.end_date" />
                                </fieldset>
                                <fieldset class="space-y-2">
                                    <legend class="px-1 text-sm font-medium">2. Select Shift & Zone</legend>
                                    <div><Label for="shift">Shift</Label><Select v-model="form.shift_id">
                                            <SelectTrigger id="shift" class="mt-1">
                                                <SelectValue placeholder="Select a shift..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="shift in shifts" :key="shift.id" :value="shift.id">{{
                                                    shift.name }}</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <InputError class="mt-2" :message="form.errors.shift_id" />
                                    </div>
                                    <div><Label for="work_zone">Work Zone</Label><Select v-model="form.work_zone_id">
                                            <SelectTrigger id="work_zone" class="mt-1">
                                                <SelectValue placeholder="Select a work zone..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="zone in work_zones" :key="zone.id" :value="zone.id">
                                                    {{ zone.name }}</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <InputError class="mt-2" :message="form.errors.work_zone_id" />
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <legend class="px-1 text-sm font-medium">3. Select Available Employees</legend>
                                    <Popover v-model:open="isPopoverOpen">
                                        <PopoverTrigger as-child><Button variant="outline" role="combobox"
                                                :aria-expanded="isPopoverOpen" class="w-full justify-between mt-1"
                                                :disabled="isLoadingEmployees || availableEmployees.length === 0"><span
                                                    v-if="form.employee_ids.length > 0" class="truncate">{{
                                                    form.employee_ids.length }} employee(s) selected</span><span v-else
                                                    class="text-muted-foreground">Select employees...</span>
                                                <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                            </Button></PopoverTrigger>
                                        <PopoverContent class="w-[--radix-popover-trigger-width] p-0">
                                            <Command>
                                                <div class="relative">
                                                    <Search
                                                        class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                                    <CommandInput placeholder="Search employees..." class="pl-8" />
                                                </div>
                                                <CommandList>
                                                    <CommandEmpty>No employees found for this date range.</CommandEmpty>
                                                    <CommandGroup>
                                                        <CommandItem v-for="employee in availableEmployees"
                                                            :key="employee.id"
                                                            :value="`${employee.first_name} ${employee.last_name}`"
                                                            @select="(ev) => { if (typeof ev.detail.value !== 'string') return; const index = form.employee_ids.indexOf(employee.id); if (index > -1) { form.employee_ids.splice(index, 1); } else { form.employee_ids.push(employee.id); } }">
                                                            <Check
                                                                :class="cn('mr-2 h-4 w-4', form.employee_ids.includes(employee.id) ? 'opacity-100' : 'opacity-0')" />
                                                            {{ employee.first_name }} {{ employee.last_name }}
                                                        </CommandItem>
                                                    </CommandGroup>
                                                </CommandList>
                                            </Command>
                                        </PopoverContent>
                                    </Popover>
                                    <InputError class="mt-2" :message="form.errors.employee_ids" />
                                    <div v-if="selectedEmployees.length > 0" class="pt-2 flex flex-wrap gap-1">
                                        <Badge v-for="employee in selectedEmployees" :key="`selected-${employee.id}`"
                                            variant="secondary" class="font-normal">{{ employee.first_name }} {{
                                            employee.last_name }}</Badge>
                                    </div>
                                </fieldset>
                            </CardContent>
                        </Card>
                        <div class="flex justify-end mt-4"><Button type="submit"
                                :class="{ 'opacity-25': form.processing }"
                                :disabled="form.employee_ids.length === 0 || !form.shift_id || !form.work_zone_id || form.processing">Assign
                                Shift(s)</Button></div>
                    </form>
                </div>

                <div class="lg:col-span-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Current Schedule</CardTitle>
                            <CardDescription>View, filter, and manage all assigned shifts.</CardDescription>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <Input
                                placeholder="Filter by employee name..."
                                :model-value="assignmentTable.getColumn('employeeName')?.getFilterValue() || ''"
                                @update:model-value="assignmentTable.getColumn('employeeName')?.setFilterValue($event)"
                                class="max-w-sm"
                            />
                            <!-- <Input class="max-w-xs" placeholder="Filter by first name..." :model-value="table.getColumn('first_name')?.getFilterValue()" @update:model-value="table.getColumn('first_name')?.setFilterValue($event)" /> -->
                            <div class="hidden rounded-md border md:block">
                                <Table>
                                    <TableHeader>
                                        <TableRow v-for="headerGroup in assignmentTable.getHeaderGroups()"
                                            :key="headerGroup.id">
                                            <TableHead v-for="header in headerGroup.headers" :key="header.id">
                                                <FlexRender v-if="!header.isPlaceholder"
                                                    :render="header.column.columnDef.header"
                                                    :props="header.getContext()" />
                                            </TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody><template v-if="assignmentTable.getRowModel().rows?.length">
                                            <TableRow v-for="row in assignmentTable.getRowModel().rows" :key="row.id">
                                                <TableCell v-for="cell in row.getVisibleCells()" :key="cell.id">
                                                    <FlexRender :render="cell.column.columnDef.cell"
                                                        :props="cell.getContext()" />
                                                </TableCell>
                                            </TableRow>
                                        </template>
                                        <TableRow v-else>
                                            <TableCell :colspan="assignmentColumns.length" class="h-24 text-center">No
                                                assignments found.</TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>
                            <div class="grid gap-4 md:hidden">
                                <div v-if="!assignmentTable.getRowModel().rows?.length"
                                    class="text-center text-muted-foreground col-span-full">No assignments found.</div>
                                <Card v-for="row in assignmentTable.getRowModel().rows" :key="`mobile-${row.id}`"
                                    @click="toggleRow(row.original.id)">
                                    <CardHeader class="pb-4">
                                        <div class="flex items-start justify-between">
                                            <div>
                                                <CardTitle class="text-lg">{{ row.original.employee?.first_name }} {{
                                                    row.original.employee?.last_name }}</CardTitle>
                                                <CardDescription>{{ row.original.shift?.name || 'N/A' }}
                                                </CardDescription>
                                            </div><Button variant="ghost" size="icon" class="h-8 w-8"
                                                @click.stop="confirmAssignmentDeletion(row.original)">
                                                <Trash2 class="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </CardHeader>
                                    <CardContent v-if="expandedRows[row.original.id]" @click.stop
                                        class="border-t pt-4 space-y-2">
                                        <p class="flex justify-between text-sm text-muted-foreground"><span>Work
                                                Zone:</span> <span class="font-medium text-foreground">{{
                                                row.original.work_zone?.name || 'N/A' }}</span></p>
                                        <p class="flex justify-between text-sm text-muted-foreground">
                                            <span>Dates:</span> <span class="font-medium text-foreground">{{ new
                                                Date(row.original.start_date).toLocaleDateString() }} - {{
                                                row.original.end_date ? new
                                                Date(row.original.end_date).toLocaleDateString() : 'Ongoing' }}</span>
                                        </p>
                                    </CardContent>
                                </Card>
                            </div>
                            <div class="flex items-center justify-end space-x-2 py-4"><Button variant="outline"
                                    size="sm" :disabled="!assignmentTable.getCanPreviousPage()"
                                    @click="assignmentTable.previousPage()">Previous</Button><Button variant="outline"
                                    size="sm" :disabled="!assignmentTable.getCanNextPage()"
                                    @click="assignmentTable.nextPage()">Next</Button></div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>

        <Modal :show="confirmingAssignmentDeletion" @close="closeAssignmentModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">Are you sure?</h2>
                <p v-if="assignmentToDelete" class="mt-1 text-sm text-gray-600">You are about to delete the assignment
                    for **{{ assignmentToDelete.employee?.first_name }} {{ assignmentToDelete.employee?.last_name }}**.
                </p>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeAssignmentModal">Cancel</SecondaryButton>
                    <DangerButton class="ms-3" @click="deleteAssignment">Delete Assignment</DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>
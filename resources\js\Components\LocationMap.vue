<script setup>
import { ref, computed } from 'vue';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  L<PERSON>ark<PERSON>,
} from "@vue-leaflet/vue-leaflet";

const props = defineProps({
    latitude: {
        type: Number,
        required: true,
    },
    longitude: {
        type: Number,
        required: true,
    },
});

const zoom = ref(16); // A good zoom level to see the immediate area

// The map's center coordinates, derived from the props
const center = computed(() => [props.latitude, props.longitude]);

</script>

<template>
    <div style="height:200px; width:100%">
        <l-map ref="map" v-model:zoom="zoom" :center="center" :use-global-leaflet="false">
            <l-tile-layer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                layer-type="base"
                name="OpenStreetMap"
            />
            <!-- The pin that shows the user's location -->
            <l-marker :lat-lng="center" />
        </l-map>
    </div>
</template>
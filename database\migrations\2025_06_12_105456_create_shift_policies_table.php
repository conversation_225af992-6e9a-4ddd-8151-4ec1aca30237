<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shift_policies', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shift_id')->constrained()->onDelete('cascade');

            // Grace period for checking IN
            // How many minutes AFTER shift starts is still considered on-time? (e.g., 15)
            $table->unsignedInteger('late_grace_period_minutes')->default(15);

            // Grace period for checking OUT
            // How many minutes BEFORE shift ends can one check out without it being an "early leave"?
            $table->unsignedInteger('early_checkout_grace_period_minutes')->default(10);
            
            // **NEWLY ADDED** - Grace period for staying late
            // How many minutes AFTER shift ends is still considered part of the normal shift (not overtime)?
            $table->unsignedInteger('overtime_grace_period_minutes')->default(15);

            // Pre-shift check-in window
            // How many minutes BEFORE the shift's official start time is check-in allowed?
            $table->unsignedInteger('check_in_allowance_before_minutes')->default(30);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shift_policies');
    }
};

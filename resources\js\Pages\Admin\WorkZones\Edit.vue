<script setup>
// The entire script block is IDENTICAL to the one you provided, except for removing defineOptions.
import { ref, watch, onMounted } from 'vue';
import { Head, useForm } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Button } from '@/Components/ui/button';
import InputError from '@/Components/InputError.vue';
import { LMap, LTileLayer, LMarker, LCircle } from "@vue-leaflet/vue-leaflet";
import L from 'leaflet';
import { GeoSearchControl, OpenStreetMapProvider } from 'leaflet-geosearch';

// We REMOVE defineOptions({ layout: AdminLayout });
// This is the key change to adopt the manual wrapper pattern.

const props = defineProps({
    work_zone: Object,
    existing_zones: Array,
});

const form = useForm({
    name: props.work_zone.name,
    latitude: props.work_zone.latitude,
    longitude: props.work_zone.longitude,
    radius_meters: props.work_zone.radius_meters,
});

const mapRef = ref(null);
const mapCenter = ref([form.latitude, form.longitude]);
const zoomLevel = ref(13);

function handleMapClick(event) { const { lat, lng } = event.latlng; form.latitude = lat.toFixed(7); form.longitude = lng.toFixed(7); }
function handleMarkerDrag(event) { const { lat, lng } = event.target.getLatLng(); form.latitude = lat.toFixed(7); form.longitude = lng.toFixed(7); }
watch(() => [form.latitude, form.longitude], (newLatLng) => { mapCenter.value = newLatLng; });
const onMapReady = () => { if (!mapRef.value) return; const provider = new OpenStreetMapProvider(); const searchControl = new GeoSearchControl({ provider, style: 'bar', showMarker: false, autoClose: true, keepResult: true }); mapRef.value.leafletObject.addControl(searchControl); mapRef.value.leafletObject.on('geosearch/showlocation', (result) => { const { y, x } = result.location; form.latitude = y.toFixed(7); form.longitude = x.toFixed(7); }); };
onMounted(() => { delete L.Icon.Default.prototype._getIconUrl; L.Icon.Default.mergeOptions({ iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png', iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png', shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png', }); });
const submit = () => { form.put(route('admin.work-zones.update', props.work_zone.id)); };
</script>

<template>
    <Head title="Edit Work Zone" />

    <!-- We now wrap the entire page in the AdminLayout component -->
    <AdminLayout>
        <!-- Provide the content for the header slot -->
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                Edit Work Zone: {{ work_zone.name }}
            </h2>
        </template>
        
        <!-- This page has no actions in the header, so the #actions slot is omitted. -->

        <!-- The rest of the page content goes into the default slot -->
        <div class="space-y-4">
            <form @submit.prevent="submit">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Form Inputs -->
                    <div class="lg:col-span-1 space-y-6">
                        <Card>
                            <CardHeader><CardTitle>Zone Details</CardTitle></CardHeader>
                            <CardContent class="space-y-4">
                                <div>
                                    <Label for="name">Zone Name</Label>
                                    <Input id="name" v-model="form.name" type="text" required class="mt-1" />
                                    <InputError class="mt-2" :message="form.errors.name" />
                                </div>
                                 <div>
                                    <Label for="radius">Radius (in meters)</Label>
                                    <Input id="radius" v-model="form.radius_meters" type="number" required class="mt-1" />
                                    <InputError class="mt-2" :message="form.errors.radius_meters" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader><CardTitle>Coordinates</CardTitle><CardDescription>Search or click on the map to set.</CardDescription></CardHeader>
                            <CardContent class="space-y-4">
                                 <div><Label for="latitude">Latitude</Label><Input id="latitude" v-model="form.latitude" type="number" step="any" required class="mt-1" /><InputError class="mt-2" :message="form.errors.latitude" /></div>
                                 <div><Label for="longitude">Longitude</Label><Input id="longitude" v-model="form.longitude" type="number" step="any" required class="mt-1" /><InputError class="mt-2" :message="form.errors.longitude" /></div>
                            </CardContent>
                        </Card>
                    </div>

                    <!-- Interactive Map -->
                    <div class="lg:col-span-2">
                        <div style="height:60vh; width:100%;" class="rounded-md border">
                            <LMap ref="mapRef" v-model:zoom="zoomLevel" v-model:center="mapCenter" :use-global-leaflet="true" @click="handleMapClick" @ready="onMapReady">
                                <LTileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" layer-type="base" name="OpenStreetMap"/>
                                
                                <LCircle v-for="zone in existing_zones.filter(z => z.id !== work_zone.id)" :key="zone.id" :lat-lng="[zone.latitude, zone.longitude]" :radius="zone.radius_meters" color="#f03" :fill-opacity="0.2"/>
                                <LMarker :lat-lng="[form.latitude, form.longitude]" draggable @moveend="handleMarkerDrag"/>
                                <LCircle :lat-lng="[form.latitude, form.longitude]" :radius="form.radius_meters" color="#3388ff"/>
                            </LMap>
                        </div>
                    </div>
                </div>
                 <div class="flex items-center justify-end mt-6">
                    <Button :class="{ 'opacity-25': form.processing }" :disabled="form.processing">Update Work Zone</Button>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>
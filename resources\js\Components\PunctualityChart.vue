<script setup>
import { Doughnut } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, ArcElement, CategoryScale } from 'chart.js';
import { computed } from 'vue';

ChartJS.register(Title, Tooltip, Legend, ArcElement, CategoryScale);

const props = defineProps({
    stats: Object,
});

const chartData = computed(() => {
    return {
        labels: ['On Time', 'Late', 'Absent', 'Early'],
        datasets: [{
            backgroundColor: [
                '#4ade80', // Green for on-time
                '#f87171', // Red for late
                '#94a3b8', // Gray for absent
                '#f59e0b'  // Orange for early
            ],
            data: [
                props.stats?.on_time_days || 0,
                props.stats?.days_late || 0,
                props.stats?.absent_days || 0,
                props.stats?.early_days || 0
            ],
            borderWidth: 0,
        }],
    };
});

const onTimePercentage = computed(() => {
    const onTime = props.stats?.on_time_days || 0;
    const totalWorkingDays = props.stats?.total_working_days || 0;
    if (totalWorkingDays === 0) return 100;
    return Math.round((onTime / totalWorkingDays) * 100);
});

const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '80%',
    plugins: {
        legend: { display: false },
        tooltip: {
            callbacks: {
                label: function(context) {
                    const label = context.label || '';
                    const value = context.raw || 0;
                    return `${label}: ${value} days`;
                }
            }
        }
    },
};
</script>

<template>
     <div class="relative w-full h-full flex items-center justify-center">
        <Doughnut :data="chartData" :options="chartOptions" />
         <div class="absolute flex flex-col items-center justify-center pointer-events-none">
            <span class="text-3xl font-bold">{{ onTimePercentage }}%</span>
            <span class="text-xs text-muted-foreground">Punctual</span>
        </div>
    </div>
</template>
<script setup>
// The script is nearly the same, we just REMOVE defineOptions.
import { Head, useForm } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Button } from '@/Components/ui/button';
import InputError from '@/Components/InputError.vue';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';

const props = defineProps({
    employee: Object,
});

const form = useForm({
    first_name: props.employee.first_name,
    last_name: props.employee.last_name,
    email: props.employee.user.email,
    status: props.employee.status,
});

const passwordForm = useForm({
    password: '',
    password_confirmation: '',
});

const submitDetails = () => {
    form.put(route('admin.employees.update', props.employee.id), {
        preserveScroll: true,
    });
};

const submitPassword = () => {
    passwordForm.put(route('admin.employees.password.update', props.employee.id), {
        onSuccess: () => passwordForm.reset(),
        preserveScroll: true,
    });
};
</script>

<template>
    <Head :title="`Edit Employee: ${form.first_name}`" />

    <!-- We now wrap the entire page in AdminLayout -->
    <AdminLayout>
        <!-- Provide the content for the header slot -->
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                Edit Employee: {{ employee.first_name }} {{ employee.last_name }}
            </h2>
        </template>

        <!-- The rest of the page content goes into the default slot -->
        <div class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Left Column: Employee Details -->
                <div class="lg:col-span-2">
                    <form @submit.prevent="submitDetails">
                        <Card>
                            <CardHeader><CardTitle>Employee Details</CardTitle></CardHeader>
                            <CardContent class="space-y-4">
                                <div class="grid sm:grid-cols-2 gap-4">
                                    <div>
                                        <Label for="first_name">First Name</Label>
                                        <Input id="first_name" v-model="form.first_name" type="text" class="mt-1 block w-full" required />
                                        <InputError class="mt-2" :message="form.errors.first_name" />
                                    </div>
                                    <div>
                                        <Label for="last_name">Last Name</Label>
                                        <Input id="last_name" v-model="form.last_name" type="text" class="mt-1 block w-full" required />
                                        <InputError class="mt-2" :message="form.errors.last_name" />
                                    </div>
                                </div>
                                <div>
                                    <Label for="email">Email</Label>
                                    <Input id="email" v-model="form.email" type="email" class="mt-1 block w-full" required />
                                    <InputError class="mt-2" :message="form.errors.email" />
                                </div>
                                <div>
                                    <Label for="status">Status</Label>
                                    <Select v-model="form.status">
                                        <SelectTrigger id="status" class="mt-1"><SelectValue placeholder="Select a status" /></SelectTrigger>
                                        <SelectContent><SelectItem value="active">Active</SelectItem><SelectItem value="inactive">Inactive</SelectItem><SelectItem value="on-leave">On Leave</SelectItem></SelectContent>
                                    </Select>
                                    <InputError class="mt-2" :message="form.errors.status" />
                                </div>
                                <div class="flex items-center justify-end">
                                    <Button :class="{ 'opacity-25': form.processing }" :disabled="form.processing">Update Details</Button>
                                </div>
                            </CardContent>
                        </Card>
                    </form>
                </div>

                <!-- Right Column: Update Password -->
                <div class="lg:col-span-1">
                     <form @submit.prevent="submitPassword">
                        <Card>
                            <CardHeader>
                                <CardTitle>Update Password</CardTitle>
                                <CardDescription class="pt-1">Ensure the account is using a long, random password to stay secure.</CardDescription>
                            </CardHeader>
                            <CardContent class="space-y-4">
                                <div>
                                    <Label for="password">New Password</Label>
                                    <Input id="password" v-model="passwordForm.password" type="password" class="mt-1 block w-full" required />
                                    <InputError class="mt-2" :message="passwordForm.errors.password" />
                                </div>
                                <div>
                                    <Label for="password_confirmation">Confirm Password</Label>
                                    <Input id="password_confirmation" v-model="passwordForm.password_confirmation" type="password" class="mt-1 block w-full" required />
                                    <InputError class="mt-2" :message="passwordForm.errors.password_confirmation" />
                                </div>
                                <div class="flex items-center justify-end">
                                    <Button :class="{ 'opacity-25': passwordForm.processing }" :disabled="passwordForm.processing">Reset Password</Button>
                                </div>
                            </CardContent>
                        </Card>
                    </form>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>
<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\WorkZone;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Rules\NoZoneOverlap;

class WorkZoneController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return Inertia::render('Admin/WorkZones/Index', [
            'work_zones' => WorkZone::latest()->get(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/WorkZones/Create', [
            // Pass all existing zones to the view
            'existing_zones' => WorkZone::all(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'latitude' => ['required', 'numeric', 'between:-90,90', new NoZoneOverlap],
            'longitude' => 'required|numeric|between:-180,180',
            'radius_meters' => 'required|integer|min:10',
        ]);

        WorkZone::create($validatedData);

        return redirect()->route('admin.work-zones.index')->with('success', 'Work zone created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(WorkZone $workZone)
    {
        return Inertia::render('Admin/WorkZones/Edit', [
            'work_zone' => $workZone,
            // We pass all other zones to the map for context, excluding the one being edited
            'existing_zones' => WorkZone::where('id', '!=', $workZone->id)->get(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, WorkZone $workZone)
    {
        // For the overlap validation, we need to pass the ID of the zone we are currently editing
        // so the rule can ignore it when checking for overlaps.
        $request->merge(['editing_zone_id' => $workZone->id]);

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'latitude' => ['required', 'numeric', 'between:-90,90', new NoZoneOverlap],
            'longitude' => 'required|numeric', 'between:-180,180',
            'radius_meters' => 'required', 'integer', 'min:10',
        ]);

        $workZone->update($validatedData);

        return redirect()->route('admin.work-zones.index')->with('success', 'Work zone updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(WorkZone $workZone)
    {
        $workZone->delete();

        return redirect()->route('admin.work-zones.index')->with('success', 'Work zone deleted successfully.');
    }
}
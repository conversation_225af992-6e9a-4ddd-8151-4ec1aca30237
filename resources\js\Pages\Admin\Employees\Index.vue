<script setup>
// This entire script block is IDENTICAL to your working version and is correct.
import { computed, h, ref, watch } from 'vue'
import { Head, Link, router } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import { FlexRender, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useVueTable } from '@tanstack/vue-table';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/Components/ui/table';
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuLabel } from '@/Components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Card, CardHeader, CardTitle, CardContent } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { ArrowUpDown, ChevronDown, MoreHorizontal, Check } from 'lucide-vue-next';
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';

// We REMOVE defineOptions({ layout: AdminLayout });
// We will wrap the template manually instead.

const props = defineProps({ employees: Array });
const data = computed(() => props.employees);
const columns = [ 
    { accessorKey: 'employee_id_number', header: 'ID', enableHiding: false }, 
    { accessorKey: 'first_name', header: 'First Name' }, 
    { accessorKey: 'last_name', header: 'Last Name' }, 
    { accessorKey: 'user.email', header: 'Email' }, 
    { accessorKey: 'status', header: 'Status', filterFn: 'equals' }, 
    { id: 'actions', header: () => h('div', { class: 'text-right' }, 'Actions'), enableHiding: false }, ];

// This is the working column visibility logic from your Shifts page example.
const storageKey = 'employees-table-column-visibility';
const savedVisibility = localStorage.getItem(storageKey);
const columnVisibility = ref(savedVisibility ? JSON.parse(savedVisibility) : {});
watch(columnVisibility, (newValue) => {
    localStorage.setItem(storageKey, JSON.stringify(newValue));
}, { deep: true });

const sorting = ref([]);
const columnFilters = ref([]);

const table = useVueTable({
    data: data.value,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
        get sorting() { return sorting.value },
        get columnFilters() { return columnFilters.value },
        get columnVisibility() { return columnVisibility.value },
    },
    onSortingChange: updater => sorting.value = typeof updater === 'function' ? updater(sorting.value) : updater,
    onColumnFiltersChange: updater => columnFilters.value = typeof updater === 'function' ? updater(columnFilters.value) : updater,
    onColumnVisibilityChange: (updater) => {
        columnVisibility.value = typeof updater === 'function' ? updater(columnVisibility.value) : updater
    },
});

const getStatusVariant = (status) => { return 'default'; };
const confirmingEmployeeDeletion = ref(false);
const employeeToDelete = ref(null);
const confirmEmployeeDeletion = (employee) => { employeeToDelete.value = employee; confirmingEmployeeDeletion.value = true; };
// const deleteEmployee = () => { router.delete(route('admin.employees.destroy', employeeToDelete.value.id), { onSuccess: () => closeModal() }); };
const deleteEmployee = () => {
    router.delete(route('admin.employees.destroy', employeeToDelete.value.id), {
        // This is the correct way to force a refresh of the employees prop
        preserveState: false, 
    });
};
const closeModal = () => { confirmingEmployeeDeletion.value = false; employeeToDelete.value = null; };
const expandedRows = ref({});
const toggleRow = (id) => { const isOpen = expandedRows.value[id]; expandedRows.value = {}; if (!isOpen) expandedRows.value[id] = true; };
const editEmployee = (id) => router.visit(route('admin.employees.edit', id));
</script>

<template>
    <Head title="Manage Employees" />

    <!-- ### THE FIX: Manually wrapping the entire page in the AdminLayout component ### -->
    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                Manage Employees
            </h2>
        </template>
        <template #actions>
            <Link :href="route('admin.employees.create')">
                <Button>Add New Employee</Button>
            </Link>
        </template>

        <!-- The main page content goes into the default slot automatically -->
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <Input class="max-w-xs" placeholder="Filter by first name..." :model-value="table.getColumn('first_name')?.getFilterValue()" @update:model-value="table.getColumn('first_name')?.setFilterValue($event)" />
                    <Select @update:model-value="(value) => table.getColumn('status')?.setFilterValue(value === 'all' ? '' : value)">
                        <SelectTrigger class="w-[180px]"><SelectValue placeholder="Filter by status..." /></SelectTrigger>
                        <SelectContent><SelectItem value="all">All</SelectItem><SelectItem value="active">Active</SelectItem><SelectItem value="inactive">Inactive</SelectItem><SelectItem value="on-leave">On Leave</SelectItem></SelectContent>
                    </Select>
                </div>
                <!-- This is the working column dropdown from your Shifts page example -->
                <DropdownMenu>
                    <DropdownMenuTrigger as-child><Button variant="outline">Columns <ChevronDown class="ml-2 h-4 w-4" /></Button></DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuCheckboxItem
                            v-for="column in table.getAllColumns().filter((col) => col.getCanHide())"
                            :key="column.id"
                            class="capitalize relative pl-8"
                            :checked="column.getIsVisible()"
                            @select.prevent="column.toggleVisibility(!column.getIsVisible())"
                        >
                            <span v-if="column.getIsVisible()" class="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                                <Check class="h-4 w-4" />
                            </span>
                            {{ column.id.replace(/_/g, ' ').replace('user.email', 'Email') }}
                        </DropdownMenuCheckboxItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>

            <!-- Desktop Table -->
            <div class="hidden rounded-md border md:block">
                 <Table><TableHeader><TableRow v-for="headerGroup in table.getHeaderGroups()" :key="headerGroup.id"><TableHead v-for="header in headerGroup.headers" :key="header.id" @click="header.column.getToggleSortingHandler()?.($event)" :class="{ 'cursor-pointer select-none': header.column.getCanSort() }"><FlexRender :render="header.column.columnDef.header" :props="header.getContext()" />{{ { asc: ' 🔼', desc: ' 🔽' }[header.column.getIsSorted()] }}</TableHead></TableRow></TableHeader><TableBody><TableRow v-if="!table.getRowModel().rows?.length"><TableCell :colspan="columns.length" class="h-24 text-center">No results.</TableCell></TableRow><TableRow v-for="row in table.getRowModel().rows" :key="row.id"><TableCell v-for="cell in row.getVisibleCells()" :key="cell.id"><template v-if="cell.column.id === 'status'"><Badge :variant="getStatusVariant(cell.getValue())" class="capitalize">{{ cell.getValue().replace('-', ' ') }}</Badge></template><template v-else-if="cell.column.id === 'user.email'">{{ row.original.user.email }}</template><template v-else-if="cell.column.id === 'actions'"><div class="text-right"><DropdownMenu><DropdownMenuTrigger as-child><Button variant="ghost" class="h-8 w-8 p-0"><MoreHorizontal class="h-4 w-4" /></Button></DropdownMenuTrigger><DropdownMenuContent align="end"><DropdownMenuItem @click="editEmployee(row.original.id)">Edit</DropdownMenuItem><DropdownMenuItem @click="confirmEmployeeDeletion(row.original)" class="text-red-600">Delete</DropdownMenuItem></DropdownMenuContent></DropdownMenu></div></template><template v-else><FlexRender :render="cell.column.columnDef.cell" :props="cell.getContext()" /></template></TableCell></TableRow></TableBody></Table>
            </div>

            <!-- Mobile View -->
            <div class="grid gap-4 md:hidden">
                 <div v-if="!table.getRowModel().rows?.length" class="text-center text-muted-foreground col-span-full">No results found.</div><Card v-for="row in table.getRowModel().rows" :key="`mobile-${row.id}`" @click="toggleRow(row.original.id)"><CardHeader><div class="flex items-center justify-between"><div><CardTitle class="text-lg">{{ row.original.first_name }} {{ row.original.last_name }}</CardTitle><p class="text-sm text-muted-foreground">{{ row.original.employee_id_number }}</p></div><Badge :variant="getStatusVariant(row.original.status)" class="capitalize">{{ row.original.status.replace('-', ' ') }}</Badge></div></CardHeader><CardContent v-if="expandedRows[row.original.id]" @click.stop class="space-y-4 pt-4 border-t"><div class="flex justify-between"><span class="text-muted-foreground">Email:</span><span class="font-medium text-right break-all">{{ row.original.user.email }}</span></div><div class="flex justify-between"><span class="text-muted-foreground">Hire Date:</span><span class="font-medium">{{ new Date(row.original.hire_date).toLocaleDateString() }}</span></div><div class="flex justify-end gap-2 pt-2"><Button variant="secondary" @click="editEmployee(row.original.id)">Edit</Button><Button variant="destructive" class="bg-red-600 text-white hover:bg-red-700" @click="confirmEmployeeDeletion(row.original)">Delete</Button></div></CardContent></Card>
            </div>

            <!-- Pagination -->
            <div class="flex items-center justify-between space-x-2 py-4">
                 <div class="flex-1 text-sm text-muted-foreground">Page {{ table.getState().pagination.pageIndex + 1 }} of {{ table.getPageCount() }}</div><div class="flex items-center space-x-2"><p class="text-sm font-medium">Rows per page</p><Select :model-value="`${table.getState().pagination.pageSize}`" @update:model-value="table.setPageSize"><SelectTrigger class="h-8 w-[70px]"><SelectValue :placeholder="`${table.getState().pagination.pageSize}`" /></SelectTrigger><SelectContent side="top"><SelectItem v-for="pageSize in [10, 20, 30, 40, 50]" :key="pageSize" :value="`${pageSize}`">{{ pageSize }}</SelectItem></SelectContent></Select></div><div class="flex items-center space-x-2"><Button variant="outline" size="sm" :disabled="!table.getCanPreviousPage()" @click="table.previousPage()">Previous</Button><Button variant="outline" size="sm" :disabled="!table.getCanNextPage()" @click="table.nextPage()">Next</Button></div>
            </div>
        </div>
        
        <Modal :show="confirmingEmployeeDeletion" @close="closeModal"><div class="p-6"><h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">Are you sure?</h2><p v-if="employeeToDelete" class="mt-1 text-sm text-gray-600 dark:text-gray-400">You are about to permanently delete **{{ employeeToDelete.first_name }} {{ employeeToDelete.last_name }}**. This cannot be undone.</p><div class="mt-6 flex justify-end"><SecondaryButton @click="closeModal">Cancel</SecondaryButton><DangerButton class="ms-3" @click="deleteEmployee">Delete Employee</DangerButton></div></div></Modal>
    </AdminLayout>
</template>
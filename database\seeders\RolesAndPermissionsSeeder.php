<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\PermissionRegistrar;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        // Define all permissions
        $permissions = [
            'manage-employees',
            'manage-shifts',
            'manage-workzones',
            'manage-attendances',
            'approve-leave',
            'approve-special-checkins',
            'view-reports',
            'view-analytics'
        ];

        // Create permissions
        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // --- ROLE DEFINITIONS ---

        // Create the 'Employee' role (has no admin permissions)
        $employeeRole = Role::create(['name' => 'Employee']);
        
        // Create the 'Admin' role and assign specific permissions
        $adminRole = Role::create(['name' => 'Admin']);
        $adminRole->givePermissionTo([
            'manage-employees',
            'manage-shifts',
            'manage-workzones',
            'manage-attendances',
            'approve-leave',
            'approve-special-checkins',
            'view-reports',
        ]);
        
        // Create the 'Super Admin' role (gets all permissions automatically)
        // A Gate will be defined in AuthServiceProvider to grant all access to this role.
        $superAdminRole = Role::create(['name' => 'Super Admin']);

        
        // --- ASSIGN A DEFAULT ADMIN USER ---

        // Find the first user (the one you registered with) and assign them the Super Admin role.
        // Change the email if your first user's email is different.
        $user = \App\Models\User::where('email', '<EMAIL>')->first();
        if ($user) {
            $user->assignRole($superAdminRole);
        }
    }
}

<script setup>
import { ref, watchEffect, computed } from 'vue';
import { Doughnut } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, ArcElement, CategoryScale } from 'chart.js';
import { useResizeObserver, useDebounceFn } from '@vueuse/core';

ChartJS.register(Title, Tooltip, Legend, ArcElement, CategoryScale);
const props = defineProps({ stats: Object });
const chartData = ref({});
const chartRef = ref(null);
const containerRef = ref(null);

watchEffect(() => {
    const regularHours = Math.round((props.stats?.total_regular_minutes || 0) / 60);
    const overtimeHours = Math.round((props.stats?.total_overtime_minutes || 0) / 60);
    chartData.value = {
        labels: ['Regular', 'Overtime'],
        datasets: [{ backgroundColor: ['#8884d8', '#a3a0e6'], data: [regularHours, overtimeHours], borderWidth: 0 }],
    };
});

// ### THE FIX: Debounced Resize Handler ###
const debouncedResize = useDebounceFn(() => {
    if (chartRef.value?.chart) {
        chartRef.value.chart.resize();
    }
}, 100);

useResizeObserver(containerRef, debouncedResize);

const totalHours = computed(() => Math.round(((props.stats?.total_regular_minutes || 0) + (props.stats?.total_overtime_minutes || 0)) / 60));
const chartOptions = { responsive: true, maintainAspectRatio: false, cutout: '80%', plugins: { legend: { display: false } } };
</script>

<template>
     <div ref="containerRef" class="relative w-full h-full">
        <Doughnut ref="chartRef" :data="chartData" :options="chartOptions" />
         <div class="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
            <span class="text-3xl font-bold">{{ totalHours }}</span>
            <span class="text-xs text-muted-foreground">Total Hours</span>
        </div>
    </div>
</template>
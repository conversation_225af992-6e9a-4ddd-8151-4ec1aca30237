<?php
namespace Database\Factories;
use App\Models\Shift;
use Illuminate\Database\Eloquent\Factories\Factory;

class ShiftFactory extends Factory
{
    public function definition(): array { return ['name' => 'General Shift']; }

    public function dayShift(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Day Shift',
            'start_time' => '09:00:00',
            'end_time' => '17:00:00',
            'spans_two_days' => false,
            'unpaid_break_minutes' => 60,
        ]);
    }
    
    public function nightShift(): Factory
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Night Shift',
            'start_time' => '21:00:00',
            'end_time' => '05:00:00',
            'spans_two_days' => true,
            'unpaid_break_minutes' => 60,
        ]);
    }
}
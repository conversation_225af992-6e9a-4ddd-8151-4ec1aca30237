<script setup>
import { <PERSON> } from '@inertiajs/vue3';
import { But<PERSON> } from '@/Components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/Components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/Components/ui/table';

// Tell Inertia to use our new AdminLayout for this page
import AdminLayout from '@/Layouts/AdminLayout.vue'
defineOptions({ layout: AdminLayout })


// This is where we will eventually pass real data from Laravel
const totalRevenue = '$45,231.89';
const subscriptions = '+2350';
const recentSales = [
    { name: '<PERSON>', email: '<EMAIL>', amount: '+$1,999.00'},
    { name: '<PERSON>', email: '<EMAIL>', amount: '+$39.00'},
    { name: '<PERSON>', email: '<EMAIL>', amount: '+$299.00'},
    { name: '<PERSON>', email: '<EMAIL>', amount: '+$99.00'},
    { name: '<PERSON>', email: '<EMAIL>', amount: '+$39.00'},
]

</script>

<template>
    <Head title="Admin Dashboard" />

    <!-- No AuthenticatedLayout wrapper needed anymore -->
    <div class="flex-1 space-y-4 pt-6">
        <div class="flex items-center justify-between space-y-2">
            <h2 class="text-3xl font-bold tracking-tight">
                Dashboard
            </h2>
            <div class="hidden md:flex items-center space-x-2">
                <Button>Download</Button>
            </div>
        </div>
        
        <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
             <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Total Revenue</CardTitle>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="h-4 w-4 text-muted-foreground"><path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" /></svg>
                    </CardHeader>
                    <CardContent>
                        <div class="text-2xl font-bold">{{ totalRevenue }}</div>
                        <p class="text-xs text-muted-foreground">+20.1% from last month</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Active Employees</CardTitle>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="h-4 w-4 text-muted-foreground"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" /><circle cx="9" cy="7" r="4" /><path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" /></svg>
                    </CardHeader>
                    <CardContent>
                        <div class="text-2xl font-bold">{{ subscriptions }}</div>
                        <p class="text-xs text-muted-foreground">+180.1% from last month</p>
                    </CardContent>
                </Card>
        </div>

        <div class="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-7">
            <Card class="lg:col-span-4 md:col-span-1">
                <CardHeader>
                    <CardTitle>Overview</CardTitle>
                </CardHeader>
                <CardContent class="pl-2">
                   <div class="p-16 text-center text-muted-foreground">[Chart Area]</div>
                </CardContent>
            </Card>
            <Card class="lg:col-span-3 md:col-span-1">
                <CardHeader>
                    <CardTitle>Recent Clock-Ins</CardTitle>
                    <CardDescription>26 clock-ins this month.</CardDescription>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Employee</TableHead>
                                <TableHead>Time</TableHead>
                            </TableRow>
                        </TableHeader>
                         <TableBody>
                            <TableRow v-for="sale in recentSales" :key="sale.email">
                                <TableCell>
                                    <div class="font-medium">{{ sale.name }}</div>
                                    <div class="hidden text-sm text-muted-foreground md:inline">{{ sale.email }}</div>
                                </TableCell>
                                <TableCell class="text-right">{{ sale.amount }}</TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </div>
    </div>
</template>
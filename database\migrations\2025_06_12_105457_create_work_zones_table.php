<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_zones', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Main Office", "Warehouse B"
            $table->double('latitude', 10, 7); // Using double for GPS precision
            $table->double('longitude', 10, 7);
            $table->unsignedInteger('radius_meters'); // The radius of the geofence
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_zones');
    }
};

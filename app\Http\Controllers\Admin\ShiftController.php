<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Shift;
use App\Models\ShiftPolicy;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class ShiftController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $shifts = Shift::with('policy')->latest()->get();
        return Inertia::render('Admin/Shifts/Index', ['shifts' => $shifts]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/Shifts/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Add the checkbox value to the request if it exists
        $request->merge(['spans_two_days' => $request->has('spans_two_days')]);

        // Define the base validation rules
        $rules = [
            'name' => 'required|string|max:255',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i',
            'unpaid_break_minutes' => 'required|integer|min:0',
            'spans_two_days' => 'sometimes|boolean',
            'late_grace_period_minutes' => 'required|integer|min:0',
            'early_leave_grace_period_minutes' => 'required|integer|min:0',
            'overtime_grace_period_minutes' => 'required|integer|min:0',
            'check_in_allowance_before_minutes' => 'required|integer|min:0',
        ];

        // Add conditional validation for end_time if the shift is on the same day
        if (!$request->spans_two_days) {
            $rules['end_time'] .= '|after:start_time';
        }

        $validatedData = $request->validate($rules);

        DB::transaction(function () use ($validatedData) {
            // Create the Shift
            $shift = Shift::create([
                'name' => $validatedData['name'],
                'start_time' => $validatedData['start_time'],
                'end_time' => $validatedData['end_time'],
                'unpaid_break_minutes' => $validatedData['unpaid_break_minutes'],
                'spans_two_days' => $validatedData['spans_two_days'] ?? false,
            ]);

            // Create the associated ShiftPolicy
            $shift->policy()->create([
                'late_grace_period_minutes' => $validatedData['late_grace_period_minutes'],
                'early_leave_grace_period_minutes' => $validatedData['early_leave_grace_period_minutes'],
                'overtime_grace_period_minutes' => $validatedData['overtime_grace_period_minutes'],
                'check_in_allowance_before_minutes' => $validatedData['check_in_allowance_before_minutes'],
            ]);
        });

        return redirect()->route('admin.shifts.index')->with('success', 'Shift created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Shift $shift)
    {
        // Eager load the policy relationship so we have access to it on the form
        $shift->load('policy');

        return Inertia::render('Admin/Shifts/Edit', [
            'shift' => $shift,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Shift $shift)
    {
        $request->merge(['spans_two_days' => $request->has('spans_two_days')]);

        $rules = [
            'name' => 'required|string|max:255',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i',
            'unpaid_break_minutes' => 'required|integer|min:0',
            'spans_two_days' => 'sometimes|boolean',
            'late_grace_period_minutes' => 'required|integer|min:0',
            'early_leave_grace_period_minutes' => 'required|integer|min:0',
            'overtime_grace_period_minutes' => 'required|integer|min:0',
            'check_in_allowance_before_minutes' => 'required|integer|min:0',
        ];

        if (!$request->spans_two_days) {
            $rules['end_time'] .= '|after:start_time';
        }

        $validatedData = $request->validate($rules);

        DB::transaction(function () use ($validatedData, $shift) {
            $shift->update([
                'name' => $validatedData['name'],
                'start_time' => $validatedData['start_time'],
                'end_time' => $validatedData['end_time'],
                'unpaid_break_minutes' => $validatedData['unpaid_break_minutes'],
                'spans_two_days' => $validatedData['spans_two_days'] ?? false,
            ]);

            // Update the associated policy
            $shift->policy->update([
                'late_grace_period_minutes' => $validatedData['late_grace_period_minutes'],
                'early_leave_grace_period_minutes' => $validatedData['early_leave_grace_period_minutes'],
                'overtime_grace_period_minutes' => $validatedData['overtime_grace_period_minutes'],
                'check_in_allowance_before_minutes' => $validatedData['check_in_allowance_before_minutes'],
            ]);
        });

        return redirect()->route('admin.shifts.index')->with('success', 'Shift updated successfully.');
    }

    public function destroy(Shift $shift)
    {
        $shift->delete();

        return redirect()->route('admin.shifts.index')->with('success', 'Shift deleted successfully.');
    }
}
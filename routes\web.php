<?php

// ### ADD THIS TEMPORARY DEBUG ROUTE ###
use App\Models\EmployeeProfile;
use Carbon\Carbon;
Route::get('/debug-attendances', function () {
    if (!auth()->check() || !auth()->user()->hasRole(['Super Admin', 'Admin'])) {
        return response('Unauthorized', 403);
    }
    $employee = EmployeeProfile::find(2);
    if (!$employee) return response()->json(['error' => 'Employee not found']);

    $targetMonth = now()->subMonth();
    $monthStart = $targetMonth->copy()->startOfMonth();
    $monthEnd = $targetMonth->copy()->endOfMonth();

    $attendances = $employee->attendances()
        ->whereBetween('check_in_time', [$monthStart, $monthEnd])
        ->get(['id', 'check_in_time', 'check_out_time', 'overtime_minutes', 'undertime_minutes', 'status']);
    
    return response()->json($attendances);
});
// ### END OF TEMPORARY ROUTE ###

use App\Http\Controllers\ProfileController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Http\Controllers\Admin\EmployeeController;
use App\Http\Controllers\AttendanceController;
use App\Http\Controllers\Admin\ShiftController;
use App\Http\Controllers\Admin\WorkZoneController;
use App\Http\Controllers\Admin\EmployeeShiftController;
use App\Http\Controllers\EmployeeDashboardController;

use Illuminate\Http\Request;
use App\Models\EmployeeShift;
// use Carbon\Carbon;

Route::get('/', function () {
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
});

// ### ALL AUTHENTICATED ROUTES GO IN THIS SINGLE GROUP ###
Route::middleware(['auth', 'verified'])->group(function () {
    
    // Employee-facing dashboard
    Route::get('/dashboard', [EmployeeDashboardController::class, 'index'])->name('dashboard');;

    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Attendance routes
    Route::post('/attendance/check-in', [AttendanceController::class, 'checkIn'])->name('attendance.check-in');
    Route::post('/attendance/check-out/{attendance}', [AttendanceController::class, 'checkOut'])->name('attendance.check-out');
    Route::post('/attendance/pre-check', [AttendanceController::class, 'preCheck'])->name('attendance.pre-check');
    Route::post('/attendance/validate-checkout-time/{attendance}', [AttendanceController::class, 'validateCheckoutTime'])->name('attendance.validate-checkout-time');
});

// ### ALL ADMIN ROUTES GO IN THIS SINGLE GROUP ###
Route::group([
    'prefix' => 'admin',
    'as' => 'admin.',
    'middleware' => ['auth', 'verified', 'role:Super Admin|Admin'],
], function () {

    Route::get('/dashboard', fn() => Inertia::render('Admin/Dashboard'))->name('dashboard');

    // --- Employee Routes (Corrected and Explicit) ---
    Route::get('/employees', [EmployeeController::class, 'index'])->name('employees.index');
    Route::get('/employees/create', [EmployeeController::class, 'create'])->name('employees.create');
    Route::post('/employees', [EmployeeController::class, 'store'])->name('employees.store');
    // All routes now consistently use {employeeProfile}
    Route::get('/employees/{employeeProfile}/edit', [EmployeeController::class, 'edit'])->name('employees.edit');
    Route::put('/employees/{employeeProfile}', [EmployeeController::class, 'update'])->name('employees.update');
    Route::delete('/employees/{employeeProfile}', [EmployeeController::class, 'destroy'])->name('employees.destroy');
    Route::put('/employees/{employeeProfile}/password', [EmployeeController::class, 'updatePassword'])->name('employees.password.update');

    // --- Shift Routes (Using resource for simplicity, as it works) ---
    Route::resource('shifts', App\Http\Controllers\Admin\ShiftController::class)->except(['show']);

    // --- Work Zone Routes (Using resource) ---
    Route::resource('work-zones', App\Http\Controllers\Admin\WorkZoneController::class)->except(['show']);
    
    // --- Shift Assignment Routes (Explicit and Correct) ---
    Route::get('/shift-assignments', [App\Http\Controllers\Admin\EmployeeShiftController::class, 'index'])->name('shift-assignments.index');
    Route::post('/shift-assignments', [App\Http\Controllers\Admin\EmployeeShiftController::class, 'store'])->name('shift-assignments.store');
    Route::get('/shift-assignments/available-employees', [App\Http\Controllers\Admin\EmployeeShiftController::class, 'getAvailableEmployees'])->name('shift-assignments.available-employees');
    // The parameter here MUST match the one in the controller method type-hint
    Route::delete('/shift-assignments/{employeeShift}', [App\Http\Controllers\Admin\EmployeeShiftController::class, 'destroy'])->name('shift-assignments.destroy');
});

require __DIR__.'/auth.php';
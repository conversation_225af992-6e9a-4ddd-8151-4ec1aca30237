<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Carbon\Carbon;
use App\Models\Attendance;
use App\Models\EmployeeProfile;
use App\Models\EmployeeShift;

class EmployeeDashboardController extends Controller
{
    /**
     * Display the employee's main dashboard.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $employeeProfile = $user->employeeProfile;

        if (!$employeeProfile) {
            if ($user->hasRole(['Super Admin', 'Admin'])) {
                return redirect()->route('admin.dashboard');
            }
            Auth::logout();
            return redirect('/login')->with('error', 'Your employee profile is not set up.');
        }

        $year = $request->input('year', now()->year);
        $month = $request->input('month', now()->month);
        $displayDate = Carbon::createFromDate($year, $month, 1)->startOfMonth();

        // Independent date range for 7-day activity chart
        $weeklyStartDate = $request->input('weekly_start_date', now()->subDays(6)->format('Y-m-d'));
        $weeklyEndDate = $request->input('weekly_end_date', now()->format('Y-m-d'));







        // DEBUG: Log request parameters to understand month navigation interference
        \Log::info('=== REQUEST PARAMETERS DEBUG ===', [
            'all_request_params' => $request->all(),
            'weekly_start_date_param' => $request->input('weekly_start_date'),
            'weekly_end_date_param' => $request->input('weekly_end_date'),
            'year_param' => $request->input('year'),
            'month_param' => $request->input('month'),
            'calculated_weekly_start' => $weeklyStartDate,
            'calculated_weekly_end' => $weeklyEndDate,
        ]);
        
        $monthStart = $displayDate->copy()->startOfMonth();
        $monthEnd = $displayDate->copy()->endOfMonth();

        $todaysAssignment = EmployeeShift::with(['shift.policy', 'workZone'])
            ->where('employee_profile_id', $employeeProfile->id)
            ->whereDate('start_date', '<=', today())
            ->where(fn ($q) => $q->whereDate('end_date', '>=', today())->orWhereNull('end_date'))
            ->first();

        $currentAttendance = $employeeProfile->attendances()->whereDate('check_in_time', today())->whereNull('check_out_time')->first();
        
        $baseMonthlyQuery = $employeeProfile->attendances()->whereBetween('check_in_time', [$monthStart, $monthEnd]);

        $monthlyAttendances = (clone $baseMonthlyQuery)->with('shift')->get();

        // --- NEW SCHEDULED HOURS CALCULATION ---
        $assignedShiftsInMonth = EmployeeShift::where('employee_profile_id', $employeeProfile->id)
            ->where(function ($query) use ($monthStart, $monthEnd) {
                $query->where('start_date', '<=', $monthEnd)
                    ->where(fn($q) => $q->where('end_date', '>=', $monthStart)->orWhereNull('end_date'));
            })
            ->with('shift')
            ->get();
        
        $totalScheduledMinutes = 0;
        // We iterate from the start to the end of the month to count working days
        for ($day = $monthStart->copy(); $day->lte($monthEnd); $day->addDay()) {
            if ($day->isWeekend()) continue;
            
            // Find which shift assignment was active on this specific day
            $activeAssignment = $assignedShiftsInMonth->first(function ($assignment) use ($day) {
                $starts = Carbon::parse($assignment->start_date);
                $ends = $assignment->end_date ? Carbon::parse($assignment->end_date) : null;
                return $day->between($starts, $ends ?? now()->addYear());
            });

            if ($activeAssignment && $activeAssignment->shift) {
                $start = Carbon::parse($activeAssignment->shift->start_time);
                $end = Carbon::parse($activeAssignment->shift->end_time);
                if ($activeAssignment->shift->spans_two_days) $end->addDay();
                $totalScheduledMinutes += $start->diffInMinutes($end) - $activeAssignment->shift->unpaid_break_minutes;
            }
        }
        
        $totalOvertime = (clone $baseMonthlyQuery)->sum('overtime_minutes');
        $totalUndertime = (clone $baseMonthlyQuery)->sum('undertime_minutes');
        $daysLate = $monthlyAttendances->where('status', 'late')->count();

        $onTimeDays = $monthlyAttendances->where('status', 'on_time')->count();

        // Calculate absent days and early days for enhanced stats
        $totalWorkingDaysInMonth = 0;
        for ($day = $monthStart->copy(); $day->lte($monthEnd); $day->addDay()) {
            if (!$day->isWeekend()) $totalWorkingDaysInMonth++;
        }

        $totalAttendanceDays = $monthlyAttendances->count();
        $absentDays = max(0, $totalWorkingDaysInMonth - $totalAttendanceDays);

        // Count early checkout days (attendances with undertime > 0)
        $earlyDays = $monthlyAttendances->where('undertime_minutes', '>', 0)->count();

        $totalWorkedMinutes = $monthlyAttendances->sum(function ($att) {
            if (!$att->check_out_time || !$att->shift) return 0;
            return Carbon::parse($att->check_in_time)->diffInMinutes(Carbon::parse($att->check_out_time)) - $att->shift->unpaid_break_minutes;
        });
        $totalRegularMinutes = $totalWorkedMinutes - $totalOvertime;

        // ### INDEPENDENT 7-day activity chart date range ###
        // Use the independent date range for weekly activity (not tied to month selector)
        $weeklyQueryStart = Carbon::parse($weeklyStartDate)->startOfDay();
        $weeklyQueryEnd = Carbon::parse($weeklyEndDate)->endOfDay();

        // Get employee's shift assignments for the last 7 days to determine working days
        $weeklyShiftAssignments = EmployeeShift::where('employee_profile_id', $employeeProfile->id)
            ->where(function ($query) use ($weeklyQueryStart, $weeklyQueryEnd) {
                $query->where('start_date', '<=', $weeklyQueryEnd->toDateString())
                    ->where(fn($q) => $q->where('end_date', '>=', $weeklyQueryStart->toDateString())->orWhereNull('end_date'));
            })
            ->with('shift')
            ->get();



        // Determine which days the employee should be working
        $workingDays = [];
        for ($day = $weeklyQueryStart->copy(); $day->lte($weeklyQueryEnd); $day->addDay()) {
            $dayName = $day->format('D');
            $dateString = $day->format('Y-m-d');

            // Check if employee has a shift assignment for this day
            $hasShiftAssignment = $weeklyShiftAssignments->first(function ($assignment) use ($day) {
                $starts = Carbon::parse($assignment->start_date);
                $ends = $assignment->end_date ? Carbon::parse($assignment->end_date) : null;
                return $day->between($starts, $ends ?? now()->addYear());
            });



            // Include weekdays by default, weekends only if there's a shift assignment
            if (!$day->isWeekend() || $hasShiftAssignment) {
                $workingDays[] = $dayName;
            }
        }

        // Get all attendance records for debugging
        $weeklyAttendanceRecords = $employeeProfile->attendances()
            ->with('shift') // Load shift information
            ->whereBetween('check_in_time', [$weeklyQueryStart, $weeklyQueryEnd])
            ->orderBy('id', 'desc') // Order by ID to get most recent records first
            ->get();





        $weeklyActivityData = $weeklyAttendanceRecords
            ->groupBy(function ($attendance) {
                return $attendance->check_in_time->format('Y-m-d');
            })
            ->map(function ($dayAttendances, $date) {
                // Get the latest attendance for each day (by ID)
                $latestAttendance = $dayAttendances->first();
                $dayName = Carbon::parse($date)->format('D');
                $isIncomplete = !$latestAttendance->check_out_time;







                // For incomplete attendance (missing check-out), calculate hours from check-in to now
                // but cap it at a reasonable maximum to avoid inflated hours
                if (!$latestAttendance->check_out_time) {
                    $checkInTime = Carbon::parse($latestAttendance->check_in_time);
                    $now = now();

                    // If check-in was today, calculate hours from check-in to now
                    if ($checkInTime->isToday()) {
                        $minutes = $checkInTime->diffInMinutes($now);
                        // Cap at 12 hours to prevent unrealistic values
                        $minutes = min($minutes, 12 * 60);
                        $hours = round($minutes / 60, 2);



                        return $hours;
                    } else {
                        // For past days with incomplete attendance, show minimal hours (0.5h)
                        // to indicate they checked in but didn't check out
                        return 0.5;
                    }
                }

                $minutes = $latestAttendance->check_in_time->diffInMinutes($latestAttendance->check_out_time);
                $hours = round($minutes / 60, 2);



                return $hours;
            })
            ->mapWithKeys(function ($hours, $date) {
                $dayName = Carbon::parse($date)->format('D');
                return [$dayName => $hours];
            });

        // Get shift information for each day in the weekly period
        $weeklyShiftData = [];
        for ($day = $weeklyQueryStart->copy(); $day->lte($weeklyQueryEnd); $day->addDay()) {
            $dayName = $day->format('D');

            // Find the shift assignment for this day
            $activeAssignment = $weeklyShiftAssignments->first(function ($assignment) use ($day) {
                $starts = Carbon::parse($assignment->start_date);
                $ends = $assignment->end_date ? Carbon::parse($assignment->end_date) : null;
                return $day->between($starts, $ends ?? now()->addYear());
            });

            if ($activeAssignment && in_array($dayName, $workingDays)) {
                $shift = $activeAssignment->shift;
                $startTime = Carbon::parse($shift->start_time);
                $endTime = Carbon::parse($shift->end_time);

                // Handle overnight shifts correctly
                if ($shift->spans_two_days) {
                    $endTime->addDay();
                }

                $expectedHours = $startTime->diffInMinutes($endTime) / 60;

                $weeklyShiftData[$dayName] = [
                    'start_time' => $shift->start_time,
                    'end_time' => $shift->end_time,
                    'expected_hours' => round($expectedHours, 2),
                    'shift_name' => $shift->name,
                ];
            }
        }

        // Only include working days in the final data
        $filteredWeeklyData = [];
        foreach ($workingDays as $day) {
            $filteredWeeklyData[$day] = $weeklyActivityData[$day] ?? 0;
        }

        $weeklyActivityData = $filteredWeeklyData;

        // Get incomplete attendances for the last 7 days
        // Get all weekly records with the same ordering as activity calculation
        $allWeeklyRecords = $employeeProfile->attendances()
            ->whereBetween('check_in_time', [$weeklyQueryStart, $weeklyQueryEnd])
            ->orderBy('id', 'desc') // Order by ID to get most recent records first
            ->get();



        // Group all records by date and check if the LATEST record for each day is incomplete
        // This matches the logic used for activity calculation
        $incompleteAttendances = $allWeeklyRecords
            ->groupBy(function ($attendance) {
                return $attendance->check_in_time->format('Y-m-d');
            })
            ->map(function ($dayAttendances, $date) {
                // Get the latest attendance for each day (same logic as activity calculation)
                $latestAttendance = $dayAttendances->first();
                $isIncomplete = is_null($latestAttendance->check_out_time);



                return $isIncomplete;
            })
            ->filter() // Only keep days that are incomplete (true values)
            ->mapWithKeys(function ($isIncomplete, $date) {
                $dayName = Carbon::parse($date)->format('D');
                return [$dayName => true];
            });

        // Get incomplete records for debugging (records that actually have null check_out_time)
        $incompleteAttendancesQuery = $employeeProfile->attendances()
            ->whereBetween('check_in_time', [$weeklyQueryStart, $weeklyQueryEnd])
            ->whereNull('check_out_time')
            ->get();

        // Filter incomplete attendances to only include working days
        $filteredIncompleteAttendances = [];
        foreach ($workingDays as $day) {
            $filteredIncompleteAttendances[$day] = $incompleteAttendances[$day] ?? false;
        }
        $incompleteAttendances = $filteredIncompleteAttendances;





        return Inertia::render('Dashboard', [
            'todaysAssignment' => $todaysAssignment,
            'currentAttendance' => $currentAttendance,
            'monthlyStats' => [
                'total_overtime_minutes' => (int) $totalOvertime,
                'total_undertime_minutes' => (int) $totalUndertime,
                'days_late' => $daysLate,
                'total_scheduled_minutes' => $totalScheduledMinutes,
                'on_time_days' => $onTimeDays,
                'total_regular_minutes' => ($totalScheduledMinutes - $totalUndertime),
                'absent_days' => $absentDays,
                'early_days' => $earlyDays,
                'total_working_days' => $totalWorkingDaysInMonth,
            ],
            'recentActivity' => $this->getRecentActivityForSevenDays($employeeProfile, $weeklyQueryStart, $weeklyQueryEnd),
            'weeklyActivityData' => $weeklyActivityData, // Pass the correct data
            'incompleteAttendances' => $incompleteAttendances, // Pass incomplete attendance data
            'weeklyShiftData' => $weeklyShiftData, // Pass shift information for each day
            'displayMonth' => $displayDate->format('F Y'),
            'weeklyDateRange' => [
                'start_date' => $weeklyStartDate,
                'end_date' => $weeklyEndDate,
            ],
            // DEBUG DATA - Remove after analysis
            'debugData' => [
                'employee_email' => $employeeProfile->user->email,
                'working_days' => $workingDays,
                'weekly_activity_raw' => $weeklyActivityData,
                'incomplete_attendances_raw' => $incompleteAttendances,
                'shift_assignments_count' => $weeklyShiftAssignments->count(),
                'attendance_records_count' => $weeklyAttendanceRecords->count(),

                'all_weekly_records' => $allWeeklyRecords->map(function($record) {
                    return [
                        'id' => $record->id,
                        'date' => $record->check_in_time->format('Y-m-d'),
                        'day' => $record->check_in_time->format('D'),
                        'check_in_time' => $record->check_in_time->format('Y-m-d H:i:s'),
                        'check_out_time' => $record->check_out_time ? $record->check_out_time->format('Y-m-d H:i:s') : 'NULL',
                        'status' => $record->status,
                        'shift_name' => $record->shift ? $record->shift->name : 'no_shift',
                    ];
                })->toArray(),
                'incomplete_records_details' => $incompleteAttendancesQuery->map(function($record) {
                    return [
                        'id' => $record->id,
                        'date' => $record->check_in_time->format('Y-m-d'),
                        'day' => $record->check_in_time->format('D'),
                        'check_in_time' => $record->check_in_time->format('Y-m-d H:i:s'),
                        'check_out_time' => $record->check_out_time ? $record->check_out_time->format('Y-m-d H:i:s') : 'NULL',
                        'status' => $record->status,
                    ];
                })->toArray(),
            ],
        ]);
    }

    /**
     * Get recent activity for exactly 7 days, including days with no attendance
     */
    private function getRecentActivityForSevenDays($employeeProfile, $weeklyQueryStart, $weeklyQueryEnd)
    {
        // Get all attendance records for the 7-day period
        $attendances = $employeeProfile->attendances()
            ->with('shift')
            ->whereBetween('check_in_time', [$weeklyQueryStart, $weeklyQueryEnd])
            ->orderBy('check_in_time', 'desc')
            ->get()
            ->groupBy(function ($attendance) {
                return $attendance->check_in_time->format('Y-m-d');
            })
            ->map(function ($dayAttendances) {
                // Return the latest attendance for each day
                return $dayAttendances->first();
            });

        // Create array for all 7 days
        $recentActivity = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::parse($weeklyQueryStart)->addDays($i);
            $dateString = $date->format('Y-m-d');

            if (isset($attendances[$dateString])) {
                // Add actual attendance record
                $recentActivity[] = $attendances[$dateString];
            } else {
                // Add placeholder for missing day (only if it's a working day)
                $dayName = $date->format('D');

                // Check if employee has an active assignment for this day
                $activeAssignment = $employeeProfile->shifts()
                    ->with('shift')
                    ->where('start_date', '<=', $dateString)
                    ->where(function ($query) use ($dateString) {
                        $query->where('end_date', '>=', $dateString)
                              ->orWhereNull('end_date');
                    })
                    ->first();

                if ($activeAssignment) {
                    // Only add placeholder if it's a working day (weekdays or weekends with assignments)
                    if (!$date->isWeekend() || $activeAssignment) {
                        $recentActivity[] = (object) [
                            'id' => 'placeholder-' . $dateString,
                            'check_in_time' => $date->format('Y-m-d H:i:s'),
                            'check_out_time' => null,
                            'shift' => $activeAssignment->shift,
                            'is_placeholder' => true,
                        ];
                    }
                }
            }
        }

        return array_reverse($recentActivity); // Most recent first
    }
}
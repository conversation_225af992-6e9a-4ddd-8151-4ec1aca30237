<script setup>
import { Head, useForm } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Button } from '@/Components/ui/button';
import InputError from '@/Components/InputError.vue';

const form = useForm({
    first_name: '',
    last_name: '',
    email: '',
    password: '',
    password_confirmation: '',
});

const submit = () => {
    form.post(route('admin.employees.store'), {
        onFinish: () => form.reset('password', 'password_confirmation'),
    });
};
</script>

<template>
    <Head title="Add New Employee" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                Add New Employee
            </h2>
        </template>
        
        <Card class="max-w-2xl">
            <CardHeader>
                <CardTitle>Employee Details</CardTitle>
            </CardHeader>
            <CardContent>
                <form @submit.prevent="submit">
                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <Label for="first_name">First Name</Label>
                            <Input id="first_name" v-model="form.first_name" type="text" class="mt-1 block w-full" required />
                            <InputError class="mt-2" :message="form.errors.first_name" />
                        </div>

                        <div>
                            <Label for="last_name">Last Name</Label>
                            <Input id="last_name" v-model="form.last_name" type="text" class="mt-1 block w-full" required />
                            <InputError class="mt-2" :message="form.errors.last_name" />
                        </div>
                        
                        <div>
                            <Label for="email">Email</Label>
                            <Input id="email" v-model="form.email" type="email" class="mt-1 block w-full" required />
                            <InputError class="mt-2" :message="form.errors.email" />
                        </div>

                        <div>
                            <Label for="password">Password</Label>
                            <Input id="password" v-model="form.password" type="password" class="mt-1 block w-full" required />
                            <InputError class="mt-2" :message="form.errors.password" />
                        </div>

                        <!-- ### THE CORRECTED LINE ### -->
                        <div>
                            <Label for="password_confirmation">Confirm Password</Label>
                            <!-- The v-model now correctly points to form.password_confirmation -->
                            <Input id="password_confirmation" v-model="form.password_confirmation" type="password" class="mt-1 block w-full" required />
                            <InputError class="mt-2" :message="form.errors.password_confirmation" />
                        </div>

                        <div class="flex items-center justify-end">
                            <Button :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                Create Employee
                            </Button>
                        </div>
                    </div>
                </form>
            </CardContent>
        </Card>
    </AdminLayout>
</template>
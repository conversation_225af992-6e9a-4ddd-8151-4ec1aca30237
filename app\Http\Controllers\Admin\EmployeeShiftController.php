<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmployeeProfile;
use App\Models\EmployeeShift;
use App\Models\Shift;
use App\Models\WorkZone;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class EmployeeShiftController extends Controller
{
    public function index()
    {
        // Eager load relationships for efficient querying
        $assignments = EmployeeShift::with(['employee', 'shift', 'workZone'])
            ->latest('start_date') // Order by the most recent start date
            ->get();

        return Inertia::render('Admin/Assignments/Index', [
            'employees' => EmployeeProfile::where('status', 'active')->get(['id', 'first_name', 'last_name']),
            'shifts' => Shift::all(['id', 'name']),
            'work_zones' => WorkZone::all(['id', 'name']),
            'assignments' => $assignments, // <-- Pass the assignments data
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'employee_ids' => 'required|array|min:1',
            'employee_ids.*' => 'required|exists:employee_profiles,id',
            'shift_id' => 'required|exists:shifts,id',
            'work_zone_id' => 'required|exists:work_zones,id',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        foreach ($request->employee_ids as $employeeId) {
            EmployeeShift::create([
                'employee_profile_id' => $employeeId,
                'shift_id' => $request->shift_id,
                'work_zone_id' => $request->work_zone_id,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
            ]);
        }

        return redirect()->route('admin.shift-assignments.index')->with('success', 'Shift(s) assigned successfully.');
    }

    public function getAvailableEmployees(Request $request)
    {
        $validated = $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $startDate = $validated['start_date'];
        $endDate = $validated['end_date'];

        // This is the new, more efficient query.
        $availableEmployees = EmployeeProfile::query()
            // 1. First, only find employees who are 'active'.
            //    This is the core of the validation fix.
            ->where('status', 'active')

            // 2. Then, from that active list, find those who do NOT have
            //    any shifts that overlap with the selected date range.
            ->whereDoesntHave('shifts', function ($query) use ($startDate, $endDate) {
                $query->where(function ($q) use ($startDate, $endDate) {
                    // An overlap occurs if a shift's start is within the range...
                    $q->where('start_date', '<=', $endDate)
                    // ...and its end date is after the range starts.
                    ->where('end_date', '>=', $startDate);
                });
            })
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get(['id', 'first_name', 'last_name']);

        return response()->json($availableEmployees);
    }

    public function destroy(EmployeeShift $shiftAssignment)
    {
        $shiftAssignment->delete();

        return back()->with('success', 'Assignment deleted successfully.');
    }
}
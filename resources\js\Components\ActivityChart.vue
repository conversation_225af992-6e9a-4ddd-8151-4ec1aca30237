<script setup>
import { Bar } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale } from 'chart.js';
import { computed } from 'vue';

ChartJS.register(Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale);

const props = defineProps({
    activityData: Object,
    incompleteAttendances: Object,
});

const chartData = computed(() => {
    const labels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const data = labels.map(day => props.activityData[day] || 0);
    const backgroundColors = labels.map(day => {
        // If this day has incomplete attendance, use orange color
        if (props.incompleteAttendances && props.incompleteAttendances[day]) {
            return '#f97316'; // Orange for incomplete
        }
        // If there's data (hours worked), use blue for complete
        if (props.activityData[day] > 0) {
            return '#3b82f6'; // Blue for complete
        }
        // No data, use light gray
        return '#e5e7eb';
    });

    return {
        labels,
        datasets: [{
            label: 'Hours Worked',
            backgroundColor: backgroundColors,
            data: data,
        }],
    };
});

const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
};
</script>

<template>
    <Bar :data="chartData" :options="chartOptions" />
</template>
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeProfile extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'employee_id_number',
        'first_name',
        'last_name',
        'hire_date',
        'status',
    ];
    
    /**
     * Get the user that owns the employee profile.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the shift assignments for the employee.
     * THIS IS THE NEWLY ADDED RELATIONSHIP
     */
    public function shifts() 
    { 
        return $this->hasMany(EmployeeShift::class, 'employee_profile_id'); 
    }

    public function attendances() 
    { 
        return $this->hasMany(Attendance::class); 
    }
}
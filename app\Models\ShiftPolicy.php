<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShiftPolicy extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'shift_id',
        'late_grace_period_minutes',
        'overtime_grace_period_minutes',
        'check_in_allowance_before_minutes',
        'early_leave_grace_period_minutes',
    ];

    /**
     * Get the shift that owns the policy.
     */
    public function shift(): BelongsTo
    {
        return $this->belongsTo(Shift::class);
    }
}
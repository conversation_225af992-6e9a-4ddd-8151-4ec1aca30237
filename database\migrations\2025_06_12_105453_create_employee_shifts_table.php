<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employee_shifts', function (Blueprint $table) {
            $table->id();
            // Link to the specific employee's profile
            $table->foreignId('employee_profile_id')->constrained('employee_profiles')->onDelete('cascade');
            // Link to the shift they are assigned to
            $table->foreignId('shift_id')->constrained('shifts')->onDelete('cascade');
            // The date this shift assignment becomes active
            $table->date('start_date');
            // The date this assignment ends. If NULL, the assignment is indefinite.
            $table->date('end_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_shifts');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_profile_id')->constrained('employee_profiles')->onDelete('cascade');
            $table->foreignId('shift_id')->constrained('shifts')->onDelete('cascade'); // Record which shift this was for

            // Check-in data
            $table->timestamp('check_in_time');
            $table->double('check_in_latitude', 10, 7);
            $table->double('check_in_longitude', 10, 7);
            $table->string('check_in_selfie_path');
            $table->text('check_in_notes')->nullable();

            // Check-out data
            $table->timestamp('check_out_time')->nullable();
            $table->double('check_out_latitude', 10, 7)->nullable();
            $table->double('check_out_longitude', 10, 7)->nullable();
            $table->string('check_out_selfie_path')->nullable();
            $table->text('check_out_notes')->nullable();
            
            // Status of this record
            // This incorporates our discussion about missed checkouts.
            $table->enum('status', ['on_time', 'late', 'absent', 'on_leave', 'incomplete'])->default('on_time');

            // Calculated values - filled in after checkout
            $table->integer('overtime_minutes')->default(0);
            $table->integer('undertime_minutes')->default(0);
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendances');
    }
};

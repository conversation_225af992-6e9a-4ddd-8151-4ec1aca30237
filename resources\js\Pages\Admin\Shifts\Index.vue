<script setup>
// This entire script block is IDENTICAL to the one you provided and is correct.
import { computed, h, ref, watch } from 'vue'
import { Head, Link, router } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue'; // We still need the import
import { FlexRender, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useVueTable } from '@tanstack/vue-table'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/Components/ui/table'
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/Components/ui/dropdown-menu'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select'
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button'
import { Input } from '@/Components/ui/input'
import { ArrowUpDown, ChevronDown, MoreHorizontal, Check } from 'lucide-vue-next'
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';

// We REMOVE defineOptions({ layout: AdminLayout });
// This is the key change to adopt the manual wrapper pattern.

const props = defineProps({
    shifts: Array,
});

const data = computed(() => props.shifts);
const formatTime = (time) => { if (!time) return ''; const [h, m] = time.split(':'); const hours = parseInt(h, 10); const minutes = parseInt(m, 10); const ampm = hours >= 12 ? 'PM' : 'AM'; const formattedHours = hours % 12 || 12; const formattedMinutes = minutes < 10 ? '0' + minutes : minutes; return `${formattedHours}:${formattedMinutes} ${ampm}`; };
const columns = [ { accessorKey: 'name', header: 'Shift Name' }, { accessorKey: 'start_time', header: 'Start Time' }, { accessorKey: 'end_time', header: 'End Time' }, { accessorKey: 'unpaid_break_minutes', header: 'Unpaid Break (mins)' }, { accessorKey: 'policy.late_grace_period_minutes', header: 'Late Grace (mins)' }, { id: 'actions', header: () => h('div', { class: 'text-right' }, 'Actions'), enableHiding: false }, ];
const storageKey = 'shifts-table-column-visibility';
const savedVisibility = localStorage.getItem(storageKey);
const columnVisibility = ref(savedVisibility ? JSON.parse(savedVisibility) : {});
watch(columnVisibility, (newValue) => { localStorage.setItem(storageKey, JSON.stringify(newValue)); }, { deep: true });
const table = useVueTable({ data: data.value, columns, getCoreRowModel: getCoreRowModel(), getPaginationRowModel: getPaginationRowModel(), getSortedRowModel: getSortedRowModel(), getFilteredRowModel: getFilteredRowModel(), state: { get columnVisibility() { return columnVisibility.value }, }, onColumnVisibilityChange: (updater) => { columnVisibility.value = typeof updater === 'function' ? updater(columnVisibility.value) : updater }, });
const confirmingShiftDeletion = ref(false);
const shiftToDelete = ref(null);
const confirmShiftDeletion = (shift) => { shiftToDelete.value = shift; confirmingShiftDeletion.value = true; };
const deleteShift = () => { router.delete(route('admin.shifts.destroy', shiftToDelete.value.id), { preserveState: false, onSuccess: () => closeModal() }); };
const closeModal = () => { confirmingShiftDeletion.value = false; shiftToDelete.value = null; };
const expandedRows = ref({});
const toggleRow = (id) => { const isOpen = expandedRows.value[id]; expandedRows.value = {}; if (!isOpen) expandedRows.value[id] = true; };
const editShift = (id) => router.visit(route('admin.shifts.edit', id));
</script>

<template>
    <Head title="Manage Shifts" />

    <!-- We now wrap the entire page in the AdminLayout component -->
    <AdminLayout>
        <!-- The title is provided to the "header" slot -->
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                Manage Shifts
            </h2>
        </template>
        <!-- The action button is provided to the "actions" slot -->
        <template #actions>
            <Link :href="route('admin.shifts.create')">
                <Button>Add New Shift</Button>
            </Link>
        </template>

        <!-- The rest of the page content goes into the default slot -->
        <div class="space-y-4">
            <div class="flex items-center justify-between gap-4">
                <Input class="max-w-sm" placeholder="Filter by shift name..." :model-value="table.getColumn('name')?.getFilterValue()" @update:model-value="table.getColumn('name')?.setFilterValue($event)" />
                <DropdownMenu>
                    <DropdownMenuTrigger as-child><Button variant="outline" class="hidden sm:flex">Columns <ChevronDown class="ml-2 h-4 w-4" /></Button></DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuCheckboxItem
                            v-for="column in table.getAllColumns().filter((col) => col.getCanHide())"
                            :key="column.id"
                            class="capitalize relative pl-8"
                            :checked="column.getIsVisible()"
                            @select.prevent="column.toggleVisibility(!column.getIsVisible())"
                        >
                            <span v-if="column.getIsVisible()" class="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                                <Check class="h-4 w-4" />
                            </span>
                            {{ column.id.replace(/_/g, ' ').replace(/\./g, ' - ') }}
                        </DropdownMenuCheckboxItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
            <div class="hidden rounded-md border md:block">
                <Table>
                    <TableHeader><TableRow v-for="headerGroup in table.getHeaderGroups()" :key="headerGroup.id"><TableHead v-for="header in headerGroup.headers" :key="header.id"><button v-if="header.column.getCanSort()" @click="header.column.toggleSorting(header.column.getIsSorted() === 'asc')" class="flex items-center"><FlexRender :render="header.column.columnDef.header" :props="header.getContext()" /><ArrowUpDown class="ml-2 h-4 w-4" /></button><FlexRender v-else :render="header.column.columnDef.header" :props="header.getContext()" /></TableHead></TableRow></TableHeader>
                    <TableBody>
                        <TableRow v-if="!table.getRowModel().rows?.length"><TableCell :colspan="columns.length" class="h-24 text-center">No results.</TableCell></TableRow>
                        <TableRow v-for="row in table.getRowModel().rows" :key="row.id">
                            <TableCell v-for="cell in row.getVisibleCells()" :key="cell.id">
                                <template v-if="cell.column.id === 'start_time' || cell.column.id === 'end_time'">{{ formatTime(cell.getValue()) }}</template>
                                <template v-else-if="cell.column.id === 'policy.late_grace_period_minutes'">{{ row.original.policy?.late_grace_period_minutes || 'N/A' }}</template>
                                <template v-else-if="cell.column.id === 'name'"><span class="font-medium">{{ cell.getValue() }}</span> <span v-if="row.original.spans_two_days" class="text-xs text-muted-foreground">(Overnight)</span></template>
                                <template v-else-if="cell.column.id === 'actions'"><div class="text-right"><DropdownMenu><DropdownMenuTrigger as-child><Button variant="ghost" class="h-8 w-8 p-0"><MoreHorizontal class="h-4 w-4" /></Button></DropdownMenuTrigger><DropdownMenuContent align="end"><DropdownMenuItem @click="editShift(row.original.id)">Edit</DropdownMenuItem><DropdownMenuItem @click="confirmShiftDeletion(row.original)" class="text-red-600">Delete</DropdownMenuItem></DropdownMenuContent></DropdownMenu></div></template>
                                <template v-else>{{ cell.getValue() }}</template>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
            <div class="grid gap-4 md:hidden">
                <div v-if="!table.getRowModel().rows?.length" class="text-center text-muted-foreground col-span-full">No results found.</div>
                <Card v-for="row in table.getRowModel().rows" :key="`mobile-${row.id}`" @click="toggleRow(row.original.id)">
                    <CardHeader><div class="flex items-center justify-between"><div><CardTitle class="text-lg">{{ row.original.name }}</CardTitle><CardDescription>{{ formatTime(row.original.start_time) }} - {{ formatTime(row.original.end_time) }}</CardDescription></div><span v-if="row.original.spans_two_days" class="text-xs text-muted-foreground">(Overnight)</span></div></CardHeader>
                    <CardContent v-if="expandedRows[row.original.id]" @click.stop class="space-y-4 pt-4 border-t"><div class="flex justify-between"><span class="text-muted-foreground">Unpaid Break:</span><span class="font-medium text-right">{{ row.original.unpaid_break_minutes }} mins</span></div><div class="flex justify-between"><span class="text-muted-foreground">Check-in Allowance:</span><span class="font-medium text-right">{{ row.original.policy?.check_in_allowance_before_minutes }} mins</span></div><div class="flex justify-end gap-2 pt-2"><Button variant="secondary" @click="editShift(row.original.id)">Edit</Button><Button variant="destructive" class="bg-red-600 text-white hover:bg-red-700" @click="confirmShiftDeletion(row.original)">Delete</Button></div></CardContent>
                </Card>
            </div>
            <div class="flex items-center justify-end space-x-2 py-4">
                <div class="flex items-center space-x-2"><p class="text-sm font-medium">Rows per page</p><Select :model-value="`${table.getState().pagination.pageSize}`" @update:model-value="table.setPageSize"><SelectTrigger class="h-8 w-[70px]"><SelectValue :placeholder="`${table.getState().pagination.pageSize}`" /></SelectTrigger><SelectContent side="top"><SelectItem v-for="pageSize in [10, 20, 30, 40, 50]" :key="pageSize" :value="`${pageSize}`">{{ pageSize }}</SelectItem></SelectContent></Select></div>
                <div class="flex items-center space-x-2"><Button variant="outline" size="sm" :disabled="!table.getCanPreviousPage()" @click="table.previousPage()">Previous</Button><Button variant="outline" size="sm" :disabled="!table.getCanNextPage()" @click="table.nextPage()">Next</Button></div>
            </div>
        </div>
        <Modal :show="confirmingShiftDeletion" @close="closeModal"><div class="p-6"><h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">Are you sure?</h2><p v-if="shiftToDelete" class="mt-1 text-sm text-gray-600 dark:text-gray-400">You are about to permanently delete the **{{ shiftToDelete.name }}**. This cannot be undone.</p><div class="mt-6 flex justify-end"><SecondaryButton @click="closeModal">Cancel</SecondaryButton><DangerButton class="ms-3" @click="deleteShift">Delete Shift</DangerButton></div></div></Modal>
    </AdminLayout>
</template>
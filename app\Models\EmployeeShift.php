<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmployeeShift extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_profile_id',
        'shift_id',
        'work_zone_id',
        'start_date',
        'end_date',
    ];

    public function employee(): <PERSON><PERSON><PERSON>T<PERSON>
    {
        return $this->belongsTo(EmployeeProfile::class, 'employee_profile_id');
    }

    public function shift(): BelongsTo
    {
        return $this->belongsTo(Shift::class);
    }

    public function workZone(): BelongsTo
    {
        return $this->belongsTo(WorkZone::class);
    }
}
<?php
namespace Database\Factories;
use App\Models\Attendance;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

class AttendanceFactory extends Factory
{
    public function definition(): array
    {
        return [
            'check_in_latitude' => fake()->latitude(),
            'check_in_longitude' => fake()->longitude(),
            'check_in_selfie_path' => 'selfies/placeholder.jpg',
            'check_out_selfie_path' => 'selfies/placeholder.jpg',
        ];
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    // In the new add_work_zone_id_to_employee_shifts_table.php migration file

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employee_shifts', function (Blueprint $table) {
            // Add the new column
            $table->foreignId('work_zone_id')
                ->nullable() // Making it nullable temporarily if you have existing data
                ->constrained('work_zones')
                ->onDelete('set null')
                ->after('shift_id'); // Place it after the shift_id column
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employee_shifts', function (Blueprint $table) {
            // Define how to drop the column and its foreign key
            $table->dropForeign(['work_zone_id']);
            $table->dropColumn('work_zone_id');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shift_policies', function (Blueprint $table) {
            $table->unsignedInteger('early_leave_grace_period_minutes')->default(0)->after('late_grace_period_minutes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shift_policies', function (Blueprint $table) {
            //
        });
    }
};

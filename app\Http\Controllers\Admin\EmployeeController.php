<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use App\Models\EmployeeProfile; // Import the model
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use App\Http\Controllers\Controller;
use Inertia\Inertia; // Import Inertia

class EmployeeController extends Controller
{
    public function index()
    {
        // Fetch all employee profiles along with their user data (email)
        $employees = EmployeeProfile::with('user')->latest()->get(); // Using with('user') to load the related User model

        return Inertia::render('Admin/Employees/Index', [
            'employees' => $employees,
        ]);
    }

    public function create()
    {
        // This method's only job is to return the view for the creation form.
        return Inertia::render('Admin/Employees/Create');
    }

    public function store(Request $request)
    {
        // 1. Validate the incoming request data
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        // 2. Use a database transaction.
        // This ensures that if any step fails (e.g., profile creation),
        // the user creation will be rolled back, preventing orphaned data.
        DB::transaction(function () use ($request) {
            // 3. Create the User model
            $user = User::create([
                'name' => $request->first_name . ' ' . $request->last_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
            ]);

            // 4. Assign the 'Employee' role to the new user.
            $user->assignRole('Employee');

            // 5. Create the associated EmployeeProfile
            EmployeeProfile::create([
                'user_id' => $user->id,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'employee_id_number' => 'EMP' . str_pad($user->id, 4, '0', STR_PAD_LEFT), // e.g., EMP0001
                'hire_date' => now(), // Default to today
                'status' => 'active',
            ]);
        });

        // 6. Redirect back to the employee list page.
        // We can add a success message later.
        return redirect()->route('admin.employees.index')->with('success', 'Employee created successfully.');
    }

    public function edit(EmployeeProfile $employeeProfile)
    {
        $employeeData = EmployeeProfile::with('user')->findOrFail($employeeProfile->id);

        // Return the Inertia view, passing the employee data as a prop
        return Inertia::render('Admin/Employees/Edit', [
            'employee' => $employeeData,
        ]);
    }

    public function update(Request $request, EmployeeProfile $employeeProfile)
    {
        // 1. Validate the incoming data.
        // We want to make sure the email is unique, but we need to ignore the current user's own email.
        $validatedData = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,'.$employeeProfile->user_id,
            'status' => 'required|in:active,inactive,on-leave',
        ]);

        // 2. Use a database transaction for safety.
        DB::transaction(function () use ($validatedData, $employeeProfile) {
            // 3. Update the associated User model
            $employeeProfile->user->update([
                'name' => $validatedData['first_name'] . ' ' . $validatedData['last_name'],
                'email' => $validatedData['email'],
            ]);

            // 4. Update the EmployeeProfile model
            $employeeProfile->update([
                'first_name' => $validatedData['first_name'],
                'last_name' => $validatedData['last_name'],
                'status' => $validatedData['status'],
            ]);
        });

        // 5. Redirect back to the employee list with a success message.
        return redirect()->route('admin.employees.index')->with('success', 'Employee updated successfully.');
    }

    public function destroy(EmployeeProfile $employeeProfile)
    {
        // For safety and to ensure all related data (roles, etc.) is cleaned up,
        // we will delete the User record. Because we set up our database migration
        // with ->onDelete('cascade'), deleting the user will automatically
        // delete their associated EmployeeProfile as well.
        $employeeProfile->user->delete();

        // Redirect back with a success message.
        return redirect()->route('admin.employees.index')->with('success', 'Employee deleted successfully.');
    }

    public function updatePassword(Request $request, EmployeeProfile $employeeProfile)
    {
        // 1. Validate the new password
        $request->validate([
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);
        
        // 2. Update the user's password
        $employeeProfile->user->update([
            'password' => Hash::make($request->password)
        ]);
        
        // 3. Redirect back with a success message
        return back()->with('success', 'Password updated successfully.');
    }
    
}